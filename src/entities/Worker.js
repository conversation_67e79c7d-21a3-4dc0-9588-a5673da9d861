import { BaseEntity } from './BaseEntity';

const workerSchema = {
  "name": "Worker",
  "type": "object",
  "properties": {
    "worker_id": {
      "type": "string",
      "description": "Auto-generated worker ID"
    },
    "worker_name": {
      "type": "string",
      "description": "Worker name"
    },
    "balance": {
      "type": "number",
      "description": "Worker balance amount"
    },
    "remarks": {
      "type": "string",
      "description": "Additional remarks"
    },
    "status": {
      "type": "string",
      "enum": ["active", "inactive"],
      "default": "active",
      "description": "Worker status"
    }
  },
  "required": ["worker_name"]
};

class WorkerEntity extends BaseEntity {
  constructor() {
    super('Worker', workerSchema);
  }

  // Get active workers for dropdowns
  async getActiveWorkers() {
    const workers = await this.list();
    return workers.filter(worker => worker.status === 'active');
  }

  // Get worker by name
  async getByName(name) {
    const workers = await this.list();
    return workers.find(worker => 
      worker.worker_name.toLowerCase() === name.toLowerCase()
    );
  }
}

export const Worker = new WorkerEntity();
