import { BaseEntity } from './BaseEntity';

const metalInwardSchema = {
  "name": "MetalInward",
  "type": "object",
  "properties": {
    "metal_inward_id": {
      "type": "string",
      "description": "Auto-generated metal inward ID"
    },
    "inward_date": {
      "type": "string",
      "format": "date",
      "description": "Inward date"
    },
    "customer_id": {
      "type": "string",
      "description": "Customer ID reference"
    },
    "customer_name": {
      "type": "string",
      "description": "Customer name"
    },
    "metal_id": {
      "type": "string",
      "description": "Metal ID reference"
    },
    "metal_description": {
      "type": "string",
      "description": "Metal description"
    },
    "weight": {
      "type": "number",
      "description": "Weight"
    },
    "purity_id": {
      "type": "string",
      "description": "Purity ID reference"
    },
    "purity_name": {
      "type": "string",
      "description": "Purity name"
    },
    "rate": {
      "type": "number",
      "description": "Rate"
    },
    "piece_count": {
      "type": "number",
      "description": "Number of pieces"
    },
    "net_weight": {
      "type": "number",
      "description": "Net weight"
    },
    "comments": {
      "type": "string",
      "description": "Additional comments"
    },
    "status": {
      "type": "string",
      "enum": ["active", "inactive"],
      "default": "active",
      "description": "Record status"
    }
  },
  "required": ["inward_date", "customer_id", "metal_id", "weight", "rate"]
};

class MetalInwardEntity extends BaseEntity {
  constructor() {
    super('MetalInward', metalInwardSchema);
  }

  // Get inward records by date range
  async getByDateRange(startDate, endDate) {
    const records = await this.list();
    return records.filter(record => {
      const recordDate = new Date(record.inward_date);
      return recordDate >= new Date(startDate) && recordDate <= new Date(endDate);
    });
  }

  // Get inward records by customer
  async getByCustomer(customerId) {
    const records = await this.list();
    return records.filter(record => record.customer_id === customerId);
  }
}

export const MetalInward = new MetalInwardEntity();
