import { BaseEntity } from './BaseEntity';

const metalSchema = {
  "name": "Metal",
  "type": "object",
  "properties": {
    "metal_id": {
      "type": "string",
      "description": "Auto-generated metal ID"
    },
    "metal_description": {
      "type": "string",
      "description": "Metal description"
    },
    "remarks": {
      "type": "string",
      "description": "Additional remarks"
    },
    "status": {
      "type": "string",
      "enum": ["active", "inactive"],
      "default": "active",
      "description": "Metal status"
    }
  },
  "required": ["metal_description"]
};

class MetalEntity extends BaseEntity {
  constructor() {
    super('Metal', metalSchema);
  }

  // Get active metals for dropdowns
  async getActiveMetals() {
    const metals = await this.list();
    return metals.filter(metal => metal.status === 'active');
  }

  // Get metal by description
  async getByDescription(description) {
    const metals = await this.list();
    return metals.find(metal => 
      metal.metal_description.toLowerCase() === description.toLowerCase()
    );
  }
}

export const Metal = new MetalEntity();
