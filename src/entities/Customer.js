import { BaseEntity } from './BaseEntity';

const customerSchema = {
  "name": "Customer",
  "type": "object",
  "properties": {
    "customer_id": {
      "type": "string",
      "description": "Auto-generated customer ID"
    },
    "customer_name": {
      "type": "string",
      "description": "Customer name"
    },
    "customer_balance": {
      "type": "number",
      "description": "Customer balance amount"
    },
    "mobile_no": {
      "type": "string",
      "description": "Mobile number"
    },
    "email": {
      "type": "string",
      "format": "email",
      "description": "Email address"
    },
    "address": {
      "type": "string",
      "description": "Customer address"
    },
    "remarks": {
      "type": "string",
      "description": "Additional remarks"
    },
    "status": {
      "type": "string",
      "enum": ["active", "inactive"],
      "default": "active",
      "description": "Customer status"
    }
  },
  "required": ["customer_name"]
};

class CustomerEntity extends BaseEntity {
  constructor() {
    super('Customer', customerSchema);
  }

  // Get active customers for dropdowns
  async getActiveCustomers() {
    const customers = await this.list();
    return customers.filter(customer => customer.status === 'active');
  }

  // Get customer by name
  async getByName(name) {
    const customers = await this.list();
    return customers.find(customer => 
      customer.customer_name.toLowerCase() === name.toLowerCase()
    );
  }
}

export const Customer = new CustomerEntity();
