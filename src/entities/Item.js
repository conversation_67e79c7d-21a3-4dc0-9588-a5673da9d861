import { BaseEntity } from './BaseEntity';

const itemSchema = {
  "name": "Item",
  "type": "object",
  "properties": {
    "item_id": {
      "type": "string",
      "description": "Auto-generated item ID"
    },
    "item_number": {
      "type": "string",
      "description": "Item number"
    },
    "item_description": {
      "type": "string",
      "description": "Item description"
    },
    "remarks": {
      "type": "string",
      "description": "Additional remarks"
    },
    "status": {
      "type": "string",
      "enum": ["active", "inactive"],
      "default": "active",
      "description": "Item status"
    }
  },
  "required": ["item_number", "item_description"]
};

class ItemEntity extends BaseEntity {
  constructor() {
    super('Item', itemSchema);
  }

  // Get active items for dropdowns
  async getActiveItems() {
    const items = await this.list();
    return items.filter(item => item.status === 'active');
  }

  // Get item by number
  async getByNumber(itemNumber) {
    const items = await this.list();
    return items.find(item => 
      item.item_number.toLowerCase() === itemNumber.toLowerCase()
    );
  }
}

export const Item = new ItemEntity();
