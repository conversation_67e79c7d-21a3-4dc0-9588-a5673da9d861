{"name": "Purity", "type": "object", "properties": {"purity_id": {"type": "string", "description": "Auto-generated purity ID"}, "purity_name": {"type": "string", "description": "Purity name"}, "metal_type": {"type": "string", "description": "Metal type"}, "description": {"type": "string", "description": "Purity description"}, "remarks": {"type": "string", "description": "Additional remarks"}, "status": {"type": "string", "enum": ["active", "inactive"], "default": "active", "description": "Purity status"}}, "required": ["purity_name", "metal_type"]}