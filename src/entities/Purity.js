import { BaseEntity } from './BaseEntity';

const puritySchema = {
  "name": "Purity",
  "type": "object",
  "properties": {
    "purity_id": {
      "type": "string",
      "description": "Auto-generated purity ID"
    },
    "purity_name": {
      "type": "string",
      "description": "Purity name"
    },
    "metal_type": {
      "type": "string",
      "description": "Metal type"
    },
    "description": {
      "type": "string",
      "description": "Purity description"
    },
    "remarks": {
      "type": "string",
      "description": "Additional remarks"
    },
    "status": {
      "type": "string",
      "enum": ["active", "inactive"],
      "default": "active",
      "description": "Purity status"
    }
  },
  "required": ["purity_name", "metal_type"]
};

class PurityEntity extends BaseEntity {
  constructor() {
    super('Purity', puritySchema);
  }

  // Get active purities for dropdowns
  async getActivePurities() {
    const purities = await this.list();
    return purities.filter(purity => purity.status === 'active');
  }

  // Get purities by metal type
  async getByMetalType(metalType) {
    const purities = await this.list();
    return purities.filter(purity => 
      purity.metal_type.toLowerCase() === metalType.toLowerCase() && 
      purity.status === 'active'
    );
  }
}

export const Purity = new PurityEntity();
