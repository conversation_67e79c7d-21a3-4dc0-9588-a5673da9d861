{"name": "Sale", "type": "object", "properties": {"sale_id": {"type": "string", "description": "Auto-generated sale ID"}, "sale_date": {"type": "string", "format": "date", "description": "Sale date"}, "customer_id": {"type": "string", "description": "Customer ID reference"}, "customer_name": {"type": "string", "description": "Customer name"}, "item_id": {"type": "string", "description": "Item ID reference"}, "item_description": {"type": "string", "description": "Item description"}, "melting": {"type": "string", "description": "Melting details"}, "gross_weight": {"type": "number", "description": "Gross weight"}, "st": {"type": "string", "description": "ST value"}, "en": {"type": "string", "description": "EN value"}, "thd": {"type": "string", "description": "THD value"}, "piece_count": {"type": "number", "description": "Number of pieces"}, "net_weight": {"type": "number", "description": "Net weight"}, "comments": {"type": "string", "description": "Additional comments"}}, "required": ["sale_date", "customer_id", "item_id"]}