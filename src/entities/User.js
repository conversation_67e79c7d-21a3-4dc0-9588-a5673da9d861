class UserEntity {
  constructor() {
    this.storageKey = 'jewelry_pro_user';
    this.validCredentials = {
      username: '<EMAIL>',
      password: 'Admin@1234'
    };
  }

  async login(username, password) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        if (username === this.validCredentials.username && 
            password === this.validCredentials.password) {
          const user = {
            id: 'admin_001',
            username: this.validCredentials.username,
            name: 'Administrator',
            role: 'admin',
            loginTime: new Date().toISOString()
          };
          
          localStorage.setItem(this.storageKey, JSON.stringify(user));
          resolve(user);
        } else {
          reject(new Error('Invalid username or password'));
        }
      }, 1000); // Simulate network delay
    });
  }

  async logout() {
    return new Promise((resolve) => {
      setTimeout(() => {
        localStorage.removeItem(this.storageKey);
        resolve();
      }, 500);
    });
  }

  async me() {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const userData = localStorage.getItem(this.storageKey);
        if (userData) {
          try {
            const user = JSON.parse(userData);
            resolve(user);
          } catch (error) {
            reject(new Error('Invalid user data'));
          }
        } else {
          reject(new Error('Not authenticated'));
        }
      }, 500);
    });
  }

  isAuthenticated() {
    const userData = localStorage.getItem(this.storageKey);
    return !!userData;
  }

  getCurrentUser() {
    const userData = localStorage.getItem(this.storageKey);
    if (userData) {
      try {
        return JSON.parse(userData);
      } catch (error) {
        return null;
      }
    }
    return null;
  }
}

export const User = new UserEntity();
