{"name": "Customer", "type": "object", "properties": {"customer_id": {"type": "string", "description": "Auto-generated customer ID"}, "customer_name": {"type": "string", "description": "Customer name"}, "customer_balance": {"type": "number", "description": "Customer balance amount"}, "mobile_no": {"type": "string", "description": "Mobile number"}, "email": {"type": "string", "format": "email", "description": "Email address"}, "address": {"type": "string", "description": "Customer address"}, "remarks": {"type": "string", "description": "Additional remarks"}, "status": {"type": "string", "enum": ["active", "inactive"], "default": "active", "description": "Customer status"}}, "required": ["customer_name"]}