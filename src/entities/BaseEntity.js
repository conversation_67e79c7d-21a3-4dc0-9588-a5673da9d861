import { generateId } from '../utils';
import { User } from './User';

export class BaseEntity {
  constructor(entityName, schema) {
    this.entityName = entityName;
    this.schema = schema;
    this.storageKey = `jewelry_pro_${entityName.toLowerCase()}`;
  }

  // Get all records
  async list(sortBy = null, limit = null) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const data = this.getStorageData();
        let result = [...data];
        
        // Apply sorting
        if (sortBy) {
          const isDescending = sortBy.startsWith('-');
          const field = isDescending ? sortBy.substring(1) : sortBy;
          
          result.sort((a, b) => {
            let aVal = a[field];
            let bVal = b[field];
            
            // Handle date sorting
            if (field.includes('date')) {
              aVal = new Date(aVal);
              bVal = new Date(bVal);
            }
            
            if (aVal < bVal) return isDescending ? 1 : -1;
            if (aVal > bVal) return isDescending ? -1 : 1;
            return 0;
          });
        }
        
        // Apply limit
        if (limit) {
          result = result.slice(0, limit);
        }
        
        resolve(result);
      }, 300);
    });
  }

  // Get single record by ID
  async get(id) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const data = this.getStorageData();
        const record = data.find(item => item[this.getIdField()] === id);
        
        if (record) {
          resolve(record);
        } else {
          reject(new Error(`${this.entityName} not found`));
        }
      }, 200);
    });
  }

  // Create new record
  async create(data) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        try {
          const currentUser = User.getCurrentUser();
          const now = new Date().toISOString();
          
          const newRecord = {
            ...data,
            [this.getIdField()]: generateId(this.getIdPrefix()),
            created_date: now,
            created_by: currentUser?.username || 'system',
            updated_date: now,
            updated_by: currentUser?.username || 'system',
            status: data.status || 'active'
          };
          
          const existingData = this.getStorageData();
          existingData.push(newRecord);
          this.setStorageData(existingData);
          
          resolve(newRecord);
        } catch (error) {
          reject(error);
        }
      }, 500);
    });
  }

  // Update existing record
  async update(id, data) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        try {
          const existingData = this.getStorageData();
          const index = existingData.findIndex(item => item[this.getIdField()] === id);
          
          if (index === -1) {
            reject(new Error(`${this.entityName} not found`));
            return;
          }
          
          const currentUser = User.getCurrentUser();
          const updatedRecord = {
            ...existingData[index],
            ...data,
            updated_date: new Date().toISOString(),
            updated_by: currentUser?.username || 'system'
          };
          
          existingData[index] = updatedRecord;
          this.setStorageData(existingData);
          
          resolve(updatedRecord);
        } catch (error) {
          reject(error);
        }
      }, 500);
    });
  }

  // Delete record
  async delete(id) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        try {
          const existingData = this.getStorageData();
          const index = existingData.findIndex(item => item[this.getIdField()] === id);
          
          if (index === -1) {
            reject(new Error(`${this.entityName} not found`));
            return;
          }
          
          existingData.splice(index, 1);
          this.setStorageData(existingData);
          
          resolve({ success: true });
        } catch (error) {
          reject(error);
        }
      }, 300);
    });
  }

  // Search records
  async search(query, fields = []) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const data = this.getStorageData();
        const searchFields = fields.length > 0 ? fields : Object.keys(this.schema.properties);
        
        const results = data.filter(record => {
          return searchFields.some(field => {
            const value = record[field];
            if (typeof value === 'string') {
              return value.toLowerCase().includes(query.toLowerCase());
            }
            return false;
          });
        });
        
        resolve(results);
      }, 300);
    });
  }

  // Helper methods
  getStorageData() {
    const data = localStorage.getItem(this.storageKey);
    return data ? JSON.parse(data) : [];
  }

  setStorageData(data) {
    localStorage.setItem(this.storageKey, JSON.stringify(data));
  }

  getIdField() {
    return `${this.entityName.toLowerCase()}_id`;
  }

  getIdPrefix() {
    return this.entityName.substring(0, 3).toUpperCase();
  }
}
