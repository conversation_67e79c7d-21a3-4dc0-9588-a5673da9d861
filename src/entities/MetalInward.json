{"name": "MetalInward", "type": "object", "properties": {"metal_inward_id": {"type": "string", "description": "Auto-generated metal inward ID"}, "inward_date": {"type": "string", "format": "date", "description": "Inward date"}, "customer_id": {"type": "string", "description": "Customer ID reference"}, "customer_name": {"type": "string", "description": "Customer name"}, "item_id": {"type": "string", "description": "Item ID reference"}, "item_description": {"type": "string", "description": "Item description"}, "weight": {"type": "number", "description": "Weight"}, "purity": {"type": "string", "description": "Purity"}, "rate": {"type": "number", "description": "Rate"}, "piece_count": {"type": "number", "description": "Number of pieces"}, "net_weight": {"type": "number", "description": "Net weight"}, "comments": {"type": "string", "description": "Additional comments"}}, "required": ["inward_date", "customer_id", "item_id"]}