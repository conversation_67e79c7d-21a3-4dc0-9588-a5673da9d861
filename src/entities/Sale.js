import { BaseEntity } from './BaseEntity';

const saleSchema = {
  "name": "Sale",
  "type": "object",
  "properties": {
    "sale_id": {
      "type": "string",
      "description": "Auto-generated sale ID"
    },
    "sale_date": {
      "type": "string",
      "format": "date",
      "description": "Sale date"
    },
    "customer_id": {
      "type": "string",
      "description": "Customer ID reference"
    },
    "customer_name": {
      "type": "string",
      "description": "Customer name"
    },
    "item_id": {
      "type": "string",
      "description": "Item ID reference"
    },
    "item_description": {
      "type": "string",
      "description": "Item description"
    },
    "melting": {
      "type": "string",
      "description": "Melting details"
    },
    "gross_weight": {
      "type": "number",
      "description": "Gross weight"
    },
    "st": {
      "type": "string",
      "description": "ST value"
    },
    "en": {
      "type": "string",
      "description": "EN value"
    },
    "thd": {
      "type": "string",
      "description": "THD value"
    },
    "piece_count": {
      "type": "number",
      "description": "Number of pieces"
    },
    "net_weight": {
      "type": "number",
      "description": "Net weight"
    },
    "comments": {
      "type": "string",
      "description": "Additional comments"
    },
    "status": {
      "type": "string",
      "enum": ["active", "inactive"],
      "default": "active",
      "description": "Record status"
    }
  },
  "required": ["sale_date", "customer_id", "item_id", "gross_weight", "net_weight"]
};

class SaleEntity extends BaseEntity {
  constructor() {
    super('Sale', saleSchema);
  }

  // Get sale records by date range
  async getByDateRange(startDate, endDate) {
    const records = await this.list();
    return records.filter(record => {
      const recordDate = new Date(record.sale_date);
      return recordDate >= new Date(startDate) && recordDate <= new Date(endDate);
    });
  }

  // Get sale records by customer
  async getByCustomer(customerId) {
    const records = await this.list();
    return records.filter(record => record.customer_id === customerId);
  }

  // Get sales summary
  async getSalesSummary() {
    const sales = await this.list();
    const totalSales = sales.length;
    const totalWeight = sales.reduce((sum, sale) => sum + (sale.net_weight || 0), 0);
    const totalPieces = sales.reduce((sum, sale) => sum + (sale.piece_count || 0), 0);
    
    return {
      totalSales,
      totalWeight,
      totalPieces
    };
  }
}

export const Sale = new SaleEntity();
