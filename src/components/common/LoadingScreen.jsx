import React from 'react';
import { Gem, Loader2 } from 'lucide-react';

export default function LoadingScreen({ message = "Loading..." }) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-amber-900 flex items-center justify-center">
      <div className="absolute inset-0 bg-gradient-to-br from-slate-900/80 to-amber-900/80"></div>
      
      <div className="relative z-10 text-center">
        <div className="w-20 h-20 bg-gradient-to-br from-amber-400 to-amber-600 rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-2xl animate-pulse">
          <Gem className="w-10 h-10 text-white" />
        </div>
        
        <h1 className="text-4xl font-bold text-white mb-4">Jewelry Pro</h1>
        <p className="text-amber-200 mb-8">Inventory Management System</p>
        
        <div className="flex items-center justify-center gap-3 text-white">
          <Loader2 className="w-6 h-6 animate-spin" />
          <span className="text-lg">{message}</span>
        </div>
        
        <div className="mt-8 w-64 h-1 bg-slate-700 rounded-full mx-auto overflow-hidden">
          <div className="h-full bg-gradient-to-r from-amber-400 to-amber-600 rounded-full animate-pulse"></div>
        </div>
      </div>
    </div>
  );
}
