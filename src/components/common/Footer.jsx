import React from 'react';
import { Gem, Heart } from 'lucide-react';

export default function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-slate-800 text-white py-8 mt-auto">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div className="col-span-1 md:col-span-2">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-gradient-to-br from-amber-400 to-amber-600 rounded-xl flex items-center justify-center">
                <Gem className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-bold">Jewelry Pro</h3>
                <p className="text-slate-400 text-sm">Inventory Management System</p>
              </div>
            </div>
            <p className="text-slate-300 mb-4 max-w-md">
              Complete jewelry inventory management solution for manufacturing businesses. 
              Streamline your operations with our comprehensive CRUD system.
            </p>
            <div className="flex items-center gap-2 text-slate-400">
              <span>Made with</span>
              <Heart className="w-4 h-4 text-red-500" />
              <span>for jewelry professionals</span>
            </div>
          </div>

          {/* Features */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Features</h4>
            <ul className="space-y-2 text-slate-300">
              <li>Customer Management</li>
              <li>Inventory Tracking</li>
              <li>Transaction Records</li>
              <li>Sales Management</li>
              <li>Comprehensive Reports</li>
              <li>Data Export</li>
            </ul>
          </div>

          {/* System Info */}
          <div>
            <h4 className="text-lg font-semibold mb-4">System</h4>
            <ul className="space-y-2 text-slate-300">
              <li>React 18</li>
              <li>Vite Build Tool</li>
              <li>Tailwind CSS</li>
              <li>Local Storage</li>
              <li>Responsive Design</li>
              <li>Real-time Updates</li>
            </ul>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-slate-700 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-slate-400 text-sm">
              © {currentYear} Jewelry Pro. All rights reserved.
            </div>
            <div className="flex items-center gap-6 mt-4 md:mt-0">
              <span className="text-slate-400 text-sm">Version 1.0.0</span>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-slate-400 text-sm">System Online</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
