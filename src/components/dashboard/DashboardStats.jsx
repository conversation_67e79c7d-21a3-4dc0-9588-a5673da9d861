import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "../ui/card";
import { Package, TrendingUp, Users, Coins, Gem, ArrowUpRight, ArrowDownRight, Weight, ShoppingCart } from "lucide-react";
import { Skeleton } from "../ui/skeleton";

export default function DashboardStats({ data, isLoading }) {
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <Skeleton className="h-4 w-20 mb-2" />
                  <Skeleton className="h-8 w-16" />
                </div>
                <Skeleton className="h-12 w-12 rounded-full" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const stats = [
    {
      title: "Total Customers",
      value: data.customers?.length || 0,
      icon: Users,
      color: "from-blue-500 to-blue-600",
      bgColor: "bg-blue-100",
      textColor: "text-blue-800"
    },
    {
      title: "Total Items",
      value: data.items?.length || 0,
      icon: Package,
      color: "from-emerald-500 to-emerald-600",
      bgColor: "bg-emerald-100",
      textColor: "text-emerald-800"
    },
    {
      title: "Total Sales",
      value: data.sales?.length || 0,
      icon: ShoppingCart,
      color: "from-purple-500 to-purple-600",
      bgColor: "bg-purple-100",
      textColor: "text-purple-800"
    },
    {
      title: "Active Workers",
      value: data.workers?.filter(w => w.status === 'active').length || 0,
      icon: UserCheck,
      color: "from-amber-500 to-amber-600",
      bgColor: "bg-amber-100",
      textColor: "text-amber-800"
    }
  ];

  const inventoryStats = [
    {
      title: "Items Inward",
      value: data.itemInwards?.length || 0,
      weight: data.itemInwards?.reduce((sum, item) => sum + (item.net_weight || 0), 0) || 0,
      icon: ArrowDownRight,
      color: "from-green-500 to-green-600",
      bgColor: "bg-green-100",
      textColor: "text-green-800",
      trend: "+12%",
      trendUp: true
    },
    {
      title: "Items Outward",
      value: data.itemOutwards?.length || 0,
      weight: data.itemOutwards?.reduce((sum, item) => sum + (item.net_weight || 0), 0) || 0,
      icon: ArrowUpRight,
      color: "from-red-500 to-red-600",
      bgColor: "bg-red-100",
      textColor: "text-red-800",
      trend: "+8%",
      trendUp: true
    },
    {
      title: "Metal Inward",
      value: data.metalInwards?.length || 0,
      weight: data.metalInwards?.reduce((sum, item) => sum + (item.net_weight || 0), 0) || 0,
      icon: Coins,
      color: "from-indigo-500 to-indigo-600",
      bgColor: "bg-indigo-100",
      textColor: "text-indigo-800",
      trend: "+15%",
      trendUp: true
    },
    {
      title: "Metal Outward",
      value: data.metalOutwards?.length || 0,
      weight: data.metalOutwards?.reduce((sum, item) => sum + (item.net_weight || 0), 0) || 0,
      icon: Weight,
      color: "from-orange-500 to-orange-600",
      bgColor: "bg-orange-100",
      textColor: "text-orange-800",
      trend: "+5%",
      trendUp: true
    }
  ];

  return (
    <div className="space-y-8">
      {/* Main Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <Card key={index} className="border-0 shadow-lg bg-white/80 backdrop-blur-sm hover:shadow-xl transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-slate-600 mb-1">{stat.title}</p>
                  <p className="text-3xl font-bold text-slate-800">{stat.value}</p>
                </div>
                <div className={`p-3 rounded-full ${stat.bgColor}`}>
                  <stat.icon className={`w-6 h-6 ${stat.textColor}`} />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Inventory Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {inventoryStats.map((stat, index) => (
          <Card key={index} className="border-0 shadow-lg bg-white/80 backdrop-blur-sm hover:shadow-xl transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <p className="text-sm font-medium text-slate-600 mb-1">{stat.title}</p>
                  <p className="text-2xl font-bold text-slate-800">{stat.value}</p>
                </div>
                <div className={`p-3 rounded-full ${stat.bgColor}`}>
                  <stat.icon className={`w-6 h-6 ${stat.textColor}`} />
                </div>
              </div>
              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center text-slate-600">
                  <Weight className="w-4 h-4 mr-1" />
                  <span>{stat.weight.toFixed(2)}g</span>
                </div>
                <div className={`flex items-center ${stat.trendUp ? 'text-green-600' : 'text-red-600'}`}>
                  <TrendingUp className={`w-3 h-3 mr-1 ${stat.trendUp ? '' : 'rotate-180'}`} />
                  <span className="text-xs font-medium">{stat.trend}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
