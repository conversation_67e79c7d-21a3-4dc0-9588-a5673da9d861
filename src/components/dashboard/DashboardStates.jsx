import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Package, TrendingUp, Users, Coins, Gem, ArrowUpRight, ArrowDownRight } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";

export default function DashboardStats({ data, isLoading }) {
  const stats = [
    {
      title: "Total Customers",
      value: data.customers?.length || 0,
      icon: Users,
      color: "from-blue-500 to-blue-600",
      change: "+12%",
      isIncrease: true
    },
    {
      title: "Active Items",
      value: data.items?.length || 0,
      icon: Package,
      color: "from-emerald-500 to-emerald-600",
      change: "+8%",
      isIncrease: true
    },
    {
      title: "Total Sales",
      value: data.sales?.length || 0,
      icon: TrendingUp,
      color: "from-amber-500 to-amber-600",
      change: "+23%",
      isIncrease: true
    },
    {
      title: "Workers",
      value: data.workers?.length || 0,
      icon: Gem,
      color: "from-purple-500 to-purple-600",
      change: "+5%",
      isIncrease: true
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {stats.map((stat, index) => (
        <Card key={index} className="relative overflow-hidden border-0 shadow-lg bg-white/80 backdrop-blur-sm">
          <div className={`absolute top-0 right-0 w-32 h-32 transform translate-x-8 -translate-y-8 bg-gradient-to-br ${stat.color} rounded-full opacity-10`} />
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-slate-600">
              {stat.title}
            </CardTitle>
            <div className={`p-2 rounded-xl bg-gradient-to-br ${stat.color} bg-opacity-20`}>
              <stat.icon className={`w-4 h-4 text-slate-700`} />
            </div>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="space-y-2">
                <Skeleton className="h-8 w-16" />
                <Skeleton className="h-4 w-12" />
              </div>
            ) : (
              <div>
                <div className="text-2xl font-bold text-slate-800">{stat.value}</div>
                <div className="flex items-center text-xs text-emerald-600 mt-1">
                  <ArrowUpRight className="w-3 h-3 mr-1" />
                  {stat.change} from last month
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  );
}