import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Clock, ArrowUpRight, ArrowDownLeft, Gem } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { format } from "date-fns";

export default function RecentTransactions({ data, isLoading }) {
  const allTransactions = [
    ...(data.itemInwards?.map(item => ({ ...item, type: 'Item Inward', icon: ArrowDownLeft, color: 'bg-blue-100 text-blue-800' })) || []),
    ...(data.itemOutwards?.map(item => ({ ...item, type: 'Item Outward', icon: ArrowUpRight, color: 'bg-red-100 text-red-800' })) || []),
    ...(data.sales?.map(item => ({ ...item, type: 'Sale', icon: Gem, color: 'bg-emerald-100 text-emerald-800' })) || [])
  ].sort((a, b) => new Date(b.created_date) - new Date(a.created_date)).slice(0, 8);

  return (
    <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-slate-800">
          <Clock className="w-5 h-5" />
          Recent Transactions
        </CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  <Skeleton className="w-8 h-8 rounded-full" />
                  <div>
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-3 w-24 mt-1" />
                  </div>
                </div>
                <Skeleton className="h-6 w-20" />
              </div>
            ))}
          </div>
        ) : allTransactions.length > 0 ? (
          <div className="space-y-3">
            {allTransactions.map((transaction, index) => (
              <div key={index} className="flex items-center justify-between p-4 border border-slate-200 rounded-xl hover:shadow-md transition-all duration-200 bg-white/50">
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-lg bg-slate-100">
                    <transaction.icon className="w-4 h-4 text-slate-600" />
                  </div>
                  <div>
                    <div className="font-medium text-slate-800">
                      {transaction.customer_name || 'Unknown Customer'}
                    </div>
                    <div className="text-sm text-slate-500">
                      {transaction.item_description || 'N/A'} • {format(new Date(transaction.created_date), 'MMM d, yyyy')}
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <Badge variant="secondary" className={transaction.color}>
                    {transaction.type}
                  </Badge>
                  {transaction.net_weight && (
                    <div className="text-sm text-slate-500 mt-1">
                      {transaction.net_weight}g
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-slate-500">
            <Clock className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p>No recent transactions</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}