import React, { useState, useEffect } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

export default function PurityForm({ data, onSubmit, onCancel }) {
  const [formData, setFormData] = useState({
    purity_name: '',
    metal_type: '',
    description: '',
    remarks: '',
    status: 'active',
  });

  useEffect(() => {
    if (data) setFormData(data);
  }, [data]);

  const handleChange = (e) => {
    const { id, value } = e.target;
    setFormData(prev => ({ ...prev, [id]: value }));
  };
  
  const handleSelectChange = (id, value) => {
    setFormData(prev => ({ ...prev, [id]: value }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <Label htmlFor="purity_name">Purity Name</Label>
        <Input id="purity_name" value={formData.purity_name} onChange={handleChange} required />
      </div>
       <div>
        <Label htmlFor="metal_type">Metal Type</Label>
        <Select value={formData.metal_type} onValueChange={(v) => handleSelectChange('metal_type', v)}>
          <SelectTrigger>
            <SelectValue placeholder="Select metal type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="Gold">Gold</SelectItem>
            <SelectItem value="Silver">Silver</SelectItem>
            <SelectItem value="Platinum">Platinum</SelectItem>
            <SelectItem value="Other">Other</SelectItem>
          </SelectContent>
        </Select>
      </div>
      <div>
        <Label htmlFor="description">Description</Label>
        <Input id="description" value={formData.description} onChange={handleChange} />
      </div>
      <div>
        <Label htmlFor="remarks">Remarks</Label>
        <Textarea id="remarks" value={formData.remarks} onChange={handleChange} />
      </div>
      <div>
        <Label htmlFor="status">Status</Label>
        <Select value={formData.status} onValueChange={(v) => handleSelectChange('status', v)}>
          <SelectTrigger>
            <SelectValue placeholder="Select status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="active">Active</SelectItem>
            <SelectItem value="inactive">Inactive</SelectItem>
          </SelectContent>
        </Select>
      </div>
      <div className="flex justify-end gap-2">
        <Button type="button" variant="outline" onClick={onCancel}>Cancel</Button>
        <Button type="submit">Save</Button>
      </div>
    </form>
  );
}