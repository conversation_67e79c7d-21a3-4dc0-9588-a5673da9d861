import * as React from "react"
import { cn } from "@/utils"

const Collapsible = ({ open, onOpenChange, children, ...props }) => {
  return (
    <div {...props}>
      {React.Children.map(children, (child) => {
        if (React.isValidElement(child)) {
          return React.cloneElement(child, { open, onOpenChange })
        }
        return child
      })}
    </div>
  )
}

const CollapsibleTrigger = React.forwardRef(({ className, children, open, onOpenChange, ...props }, ref) => (
  <button
    ref={ref}
    onClick={() => onOpenChange?.(!open)}
    className={cn("", className)}
    {...props}
  >
    {children}
  </button>
))
CollapsibleTrigger.displayName = "CollapsibleTrigger"

const CollapsibleContent = React.forwardRef(({ className, children, open, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      "overflow-hidden transition-all duration-200",
      open ? "max-h-96 opacity-100" : "max-h-0 opacity-0",
      className
    )}
    {...props}
  >
    <div className="pt-1">
      {children}
    </div>
  </div>
))
CollapsibleContent.displayName = "CollapsibleContent"

export { Collapsible, CollapsibleTrigger, CollapsibleContent }
