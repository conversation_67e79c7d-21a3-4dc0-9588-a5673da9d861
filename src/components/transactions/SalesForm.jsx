import React, { useState, useEffect } from 'react';
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Textarea } from "../ui/textarea";
import { SimpleSelect } from "../ui/select";
import { Customer } from '../../entities/Customer';
import { Item } from '../../entities/Item';

export default function SalesForm({ data, onSubmit, onCancel }) {
  const [formData, setFormData] = useState({
    sale_date: new Date().toISOString().slice(0, 10),
    customer_id: '',
    customer_name: '',
    item_id: '',
    item_description: '',
    melting: '',
    gross_weight: 0,
    st: '',
    en: '',
    thd: '',
    piece_count: 1,
    net_weight: 0,
    comments: '',
  });

  const [customers, setCustomers] = useState([]);
  const [items, setItems] = useState([]);

  useEffect(() => {
    async function fetchData() {
      const [customerData, itemData] = await Promise.all([Customer.list(), Item.list()]);
      setCustomers(customerData);
      setItems(itemData);
    }
    fetchData();
  }, []);

  useEffect(() => {
    if (data) setFormData(data);
  }, [data]);

  const handleChange = (e) => {
    const { id, value } = e.target;
    setFormData(prev => ({ ...prev, [id]: value }));
  };
  
  const handleSelectChange = (id, value) => {
    if (id === 'customer_id') {
      const selectedCustomer = customers.find(c => c.id === value);
      setFormData(prev => ({ ...prev, customer_id: value, customer_name: selectedCustomer?.customer_name || '' }));
    } else if (id === 'item_id') {
      const selectedItem = items.find(i => i.id === value);
      setFormData(prev => ({ ...prev, item_id: value, item_description: selectedItem?.item_description || '' }));
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4 max-h-[70vh] overflow-y-auto p-2">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="sale_date">Sale Date</Label>
          <Input id="sale_date" type="date" value={formData.sale_date} onChange={handleChange} required />
        </div>
        <div>
          <Label htmlFor="customer_id">Customer</Label>
          <Select value={formData.customer_id} onValueChange={(v) => handleSelectChange('customer_id', v)}>
            <SelectTrigger><SelectValue placeholder="Select customer" /></SelectTrigger>
            <SelectContent>{customers.map(c => <SelectItem key={c.id} value={c.id}>{c.customer_name}</SelectItem>)}</SelectContent>
          </Select>
        </div>
        <div>
          <Label htmlFor="item_id">Item</Label>
          <Select value={formData.item_id} onValueChange={(v) => handleSelectChange('item_id', v)}>
            <SelectTrigger><SelectValue placeholder="Select item" /></SelectTrigger>
            <SelectContent>{items.map(i => <SelectItem key={i.id} value={i.id}>{i.item_description}</SelectItem>)}</SelectContent>
          </Select>
        </div>
        <div>
          <Label htmlFor="melting">Melting</Label>
          <Input id="melting" value={formData.melting} onChange={handleChange} />
        </div>
        <div>
          <Label htmlFor="gross_weight">Gross Weight</Label>
          <Input id="gross_weight" type="number" value={formData.gross_weight} onChange={handleChange} />
        </div>
        <div>
          <Label htmlFor="net_weight">Net Weight</Label>
          <Input id="net_weight" type="number" value={formData.net_weight} onChange={handleChange} />
        </div>
        <div>
          <Label htmlFor="piece_count">Piece Count</Label>
          <Input id="piece_count" type="number" value={formData.piece_count} onChange={handleChange} />
        </div>
         <div>
          <Label htmlFor="st">ST</Label>
          <Input id="st" value={formData.st} onChange={handleChange} />
        </div>
         <div>
          <Label htmlFor="en">EN</Label>
          <Input id="en" value={formData.en} onChange={handleChange} />
        </div>
         <div>
          <Label htmlFor="thd">THD</Label>
          <Input id="thd" value={formData.thd} onChange={handleChange} />
        </div>
      </div>
      <div>
        <Label htmlFor="comments">Comments</Label>
        <Textarea id="comments" value={formData.comments} onChange={handleChange} />
      </div>
      <div className="flex justify-end gap-2 pt-4">
        <Button type="button" variant="outline" onClick={onCancel}>Cancel</Button>
        <Button type="submit">Save</Button>
      </div>
    </form>
  );
}