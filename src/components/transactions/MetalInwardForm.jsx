import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Customer } from '@/entities/Customer';
import { Metal } from '@/entities/Metal';
import { Purity } from '@/entities/Purity';

export default function MetalInwardForm({ data, onSubmit, onCancel }) {
  const [formData, setFormData] = useState({
    inward_date: new Date().toISOString().slice(0, 10),
    customer_id: '',
    customer_name: '',
    item_id: '', // Using item_id for metal_id
    item_description: '', // Using item_description for metal_description
    weight: 0,
    purity: '',
    rate: 0,
    piece_count: 1,
    net_weight: 0,
    comments: '',
  });

  const [customers, setCustomers] = useState([]);
  const [metals, setMetals] = useState([]);
  const [purities, setPurities] = useState([]);

  useEffect(() => {
    async function fetchData() {
      const [customerData, metalData, purityData] = await Promise.all([Customer.list(), Metal.list(), Purity.list()]);
      setCustomers(customerData);
      setMetals(metalData);
      setPurities(purityData);
    }
    fetchData();
  }, []);

  useEffect(() => {
    if (data) setFormData(data);
  }, [data]);

  const handleChange = (e) => {
    const { id, value } = e.target;
    setFormData(prev => ({ ...prev, [id]: value }));
  };
  
  const handleSelectChange = (id, value) => {
    if (id === 'customer_id') {
      const selected = customers.find(c => c.id === value);
      setFormData(prev => ({ ...prev, customer_id: value, customer_name: selected?.customer_name || '' }));
    } else if (id === 'item_id') {
      const selected = metals.find(i => i.id === value);
      setFormData(prev => ({ ...prev, item_id: value, item_description: selected?.metal_description || '' }));
    } else {
      setFormData(prev => ({ ...prev, [id]: value }));
    }
  };

  useEffect(() => {
    // Auto-calculate net_weight
    setFormData(prev => ({
      ...prev,
      net_weight: prev.weight * prev.piece_count
    }));
  }, [formData.weight, formData.piece_count]);

  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4 max-h-[70vh] overflow-y-auto p-2">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="inward_date">Inward Date</Label>
          <Input id="inward_date" type="date" value={formData.inward_date} onChange={handleChange} required />
        </div>
        <div>
          <Label htmlFor="customer_id">Customer</Label>
          <Select value={formData.customer_id} onValueChange={(v) => handleSelectChange('customer_id', v)}>
            <SelectTrigger><SelectValue placeholder="Select customer" /></SelectTrigger>
            <SelectContent>{customers.map(c => <SelectItem key={c.id} value={c.id}>{c.customer_name}</SelectItem>)}</SelectContent>
          </Select>
        </div>
        <div>
          <Label htmlFor="item_id">Metal Type</Label>
          <Select value={formData.item_id} onValueChange={(v) => handleSelectChange('item_id', v)}>
            <SelectTrigger><SelectValue placeholder="Select metal" /></SelectTrigger>
            <SelectContent>{metals.map(i => <SelectItem key={i.id} value={i.id}>{i.metal_description}</SelectItem>)}</SelectContent>
          </Select>
        </div>
        <div>
          <Label htmlFor="purity">Purity</Label>
          <Select value={formData.purity} onValueChange={(v) => handleSelectChange('purity', v)}>
            <SelectTrigger><SelectValue placeholder="Select purity" /></SelectTrigger>
            <SelectContent>{purities.map(p => <SelectItem key={p.id} value={p.purity_name}>{p.purity_name}</SelectItem>)}</SelectContent>
          </Select>
        </div>
        <div>
          <Label htmlFor="weight">Weight (g)</Label>
          <Input id="weight" type="number" step="0.01" value={formData.weight} onChange={handleChange} />
        </div>
        <div>
          <Label htmlFor="rate">Rate (per gram)</Label>
          <Input id="rate" type="number" step="0.01" value={formData.rate} onChange={handleChange} />
        </div>
        <div>
          <Label htmlFor="piece_count">Piece Count</Label>
          <Input id="piece_count" type="number" value={formData.piece_count} onChange={handleChange} />
        </div>
        <div>
          <Label htmlFor="net_weight">Net Weight (g)</Label>
          <Input id="net_weight" type="number" value={formData.net_weight} readOnly className="bg-slate-100" />
        </div>
      </div>
      <div>
        <Label htmlFor="comments">Comments</Label>
        <Textarea id="comments" value={formData.comments} onChange={handleChange} />
      </div>
      <div className="flex justify-end gap-2 pt-4">
        <Button type="button" variant="outline" onClick={onCancel}>Cancel</Button>
        <Button type="submit">Save</Button>
      </div>
    </form>
  );
}