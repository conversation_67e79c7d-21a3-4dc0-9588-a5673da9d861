import { Customer, Item, Worker, Metal, Purity, ItemInward, ItemOutward, MetalInward, MetalOutward, Sale } from '../entities/all';

export const exportAllData = async () => {
  try {
    const [
      customers,
      items,
      workers,
      metals,
      purities,
      itemInwards,
      itemOutwards,
      metalInwards,
      metalOutwards,
      sales
    ] = await Promise.all([
      Customer.list(),
      Item.list(),
      Worker.list(),
      Metal.list(),
      Purity.list(),
      ItemInward.list(),
      ItemOutward.list(),
      MetalInward.list(),
      MetalOutward.list(),
      Sale.list()
    ]);

    const allData = {
      exportDate: new Date().toISOString(),
      version: '1.0.0',
      data: {
        customers,
        items,
        workers,
        metals,
        purities,
        itemInwards,
        itemOutwards,
        metalInwards,
        metalOutwards,
        sales
      },
      summary: {
        totalCustomers: customers.length,
        totalItems: items.length,
        totalWorkers: workers.length,
        totalMetals: metals.length,
        totalPurities: purities.length,
        totalTransactions: itemInwards.length + itemOutwards.length + metalInwards.length + metalOutwards.length + sales.length
      }
    };

    const dataStr = JSON.stringify(allData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `jewelry_pro_backup_${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    return true;
  } catch (error) {
    console.error('Error exporting data:', error);
    return false;
  }
};

export const importData = async (file) => {
  try {
    const text = await file.text();
    const importedData = JSON.parse(text);
    
    if (!importedData.data) {
      throw new Error('Invalid backup file format');
    }

    // Clear existing data (optional - you might want to merge instead)
    localStorage.clear();
    
    // Import each entity type
    const { data } = importedData;
    
    if (data.customers) {
      localStorage.setItem('jewelry_pro_customer', JSON.stringify(data.customers));
    }
    
    if (data.items) {
      localStorage.setItem('jewelry_pro_item', JSON.stringify(data.items));
    }
    
    if (data.workers) {
      localStorage.setItem('jewelry_pro_worker', JSON.stringify(data.workers));
    }
    
    if (data.metals) {
      localStorage.setItem('jewelry_pro_metal', JSON.stringify(data.metals));
    }
    
    if (data.purities) {
      localStorage.setItem('jewelry_pro_purity', JSON.stringify(data.purities));
    }
    
    if (data.itemInwards) {
      localStorage.setItem('jewelry_pro_iteminward', JSON.stringify(data.itemInwards));
    }
    
    if (data.itemOutwards) {
      localStorage.setItem('jewelry_pro_itemoutward', JSON.stringify(data.itemOutwards));
    }
    
    if (data.metalInwards) {
      localStorage.setItem('jewelry_pro_metalinward', JSON.stringify(data.metalInwards));
    }
    
    if (data.metalOutwards) {
      localStorage.setItem('jewelry_pro_metaloutward', JSON.stringify(data.metalOutwards));
    }
    
    if (data.sales) {
      localStorage.setItem('jewelry_pro_sale', JSON.stringify(data.sales));
    }
    
    return true;
  } catch (error) {
    console.error('Error importing data:', error);
    return false;
  }
};
