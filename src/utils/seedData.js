import { Customer, Item, Worker, Metal, Purity, ItemInward, ItemOutward, MetalInward, MetalOutward, Sale } from '../entities/all';

export const seedInitialData = async () => {
  try {
    // Check if data already exists
    const existingCustomers = await Customer.list();
    if (existingCustomers.length > 0) {
      console.log('Data already exists, skipping seed');
      return;
    }

    console.log('Seeding initial data...');

    // Seed Customers
    const customers = [
      {
        customer_name: '<PERSON><PERSON>',
        customer_balance: 15000.50,
        mobile_no: '9876543210',
        email: 'raj<PERSON>.<EMAIL>',
        address: '123 MG Road, Bangalore, Karnataka 560001',
        remarks: 'Regular customer, prefers gold jewelry'
      },
      {
        customer_name: '<PERSON><PERSON>',
        customer_balance: 8500.00,
        mobile_no: '9876543211',
        email: '<EMAIL>',
        address: '456 Brigade Road, Bangalore, Karnataka 560025',
        remarks: 'Prefers silver and diamond jewelry'
      },
      {
        customer_name: '<PERSON><PERSON>',
        customer_balance: 22000.75,
        mobile_no: '9876543212',
        email: '<EMAIL>',
        address: '789 Commercial Street, Bangalore, Karnataka 560001',
        remarks: 'Bulk buyer, wedding jewelry specialist'
      },
      {
        customer_name: '<PERSON>ita <PERSON>',
        customer_balance: 5200.25,
        mobile_no: '9876543213',
        email: '<EMAIL>',
        address: '321 Koramangala, Bangalore, Karnataka 560034',
        remarks: 'Traditional jewelry lover'
      }
    ];

    for (const customer of customers) {
      await Customer.create(customer);
    }

    // Seed Items
    const items = [
      {
        item_number: 'RING001',
        item_description: 'Gold Diamond Ring - Classic Design',
        remarks: 'Popular wedding ring design'
      },
      {
        item_number: 'NECK001',
        item_description: 'Gold Chain Necklace - 22K',
        remarks: 'Traditional chain design'
      },
      {
        item_number: 'EAR001',
        item_description: 'Diamond Stud Earrings',
        remarks: 'Elegant daily wear earrings'
      },
      {
        item_number: 'BANG001',
        item_description: 'Gold Bangles Set - Traditional',
        remarks: 'Set of 6 bangles'
      },
      {
        item_number: 'PEND001',
        item_description: 'Ruby Pendant with Chain',
        remarks: 'Precious stone pendant'
      }
    ];

    for (const item of items) {
      await Item.create(item);
    }

    // Seed Workers
    const workers = [
      {
        worker_name: 'Ravi Goldsmith',
        balance: 2500.00,
        remarks: 'Expert in ring making and stone setting'
      },
      {
        worker_name: 'Lakshmi Artisan',
        balance: 1800.50,
        remarks: 'Specialist in traditional jewelry designs'
      },
      {
        worker_name: 'Kumar Craftsman',
        balance: 3200.75,
        remarks: 'Chain and necklace specialist'
      }
    ];

    for (const worker of workers) {
      await Worker.create(worker);
    }

    // Seed Metals
    const metals = [
      {
        metal_description: 'Gold 22K - Premium Quality',
        remarks: 'High purity gold for premium jewelry'
      },
      {
        metal_description: 'Gold 18K - Standard Quality',
        remarks: 'Standard gold for regular jewelry'
      },
      {
        metal_description: 'Silver 925 - Sterling Silver',
        remarks: 'High quality silver for jewelry making'
      },
      {
        metal_description: 'Platinum - Premium',
        remarks: 'Luxury metal for high-end jewelry'
      }
    ];

    for (const metal of metals) {
      await Metal.create(metal);
    }

    // Seed Purities
    const purities = [
      {
        purity_name: '22K Gold',
        metal_type: 'Gold',
        description: '91.6% pure gold',
        remarks: 'Traditional Indian jewelry standard'
      },
      {
        purity_name: '18K Gold',
        metal_type: 'Gold',
        description: '75% pure gold',
        remarks: 'International jewelry standard'
      },
      {
        purity_name: '925 Silver',
        metal_type: 'Silver',
        description: '92.5% pure silver',
        remarks: 'Sterling silver standard'
      },
      {
        purity_name: '950 Platinum',
        metal_type: 'Platinum',
        description: '95% pure platinum',
        remarks: 'Premium platinum standard'
      }
    ];

    for (const purity of purities) {
      await Purity.create(purity);
    }

    // Create some sample transactions
    const customerList = await Customer.list();
    const itemList = await Item.list();
    const metalList = await Metal.list();
    const purityList = await Purity.list();

    if (customerList.length > 0 && itemList.length > 0) {
      // Sample Item Inward
      await ItemInward.create({
        inward_date: '2024-01-15',
        customer_id: customerList[0].customer_id,
        customer_name: customerList[0].customer_name,
        item_id: itemList[0].item_id,
        item_description: itemList[0].item_description,
        melting: 'M001',
        gross_weight: 15.250,
        st: 'ST001',
        en: 'EN001',
        thd: 'THD001',
        piece_count: 2,
        net_weight: 14.800,
        comments: 'Customer brought old jewelry for melting'
      });

      // Sample Item Outward
      await ItemOutward.create({
        outward_date: '2024-01-20',
        customer_id: customerList[1].customer_id,
        customer_name: customerList[1].customer_name,
        item_id: itemList[1].item_id,
        item_description: itemList[1].item_description,
        melting: 'M002',
        gross_weight: 25.500,
        st: 'ST002',
        en: 'EN002',
        thd: 'THD002',
        piece_count: 1,
        net_weight: 24.750,
        comments: 'Completed jewelry delivered to customer'
      });

      // Sample Sale
      await Sale.create({
        sale_date: '2024-01-25',
        customer_id: customerList[2].customer_id,
        customer_name: customerList[2].customer_name,
        item_id: itemList[2].item_id,
        item_description: itemList[2].item_description,
        melting: 'M003',
        gross_weight: 8.750,
        st: 'ST003',
        en: 'EN003',
        thd: 'THD003',
        piece_count: 1,
        net_weight: 8.500,
        comments: 'Wedding jewelry sale'
      });
    }

    if (customerList.length > 0 && metalList.length > 0 && purityList.length > 0) {
      // Sample Metal Inward
      await MetalInward.create({
        inward_date: '2024-01-10',
        customer_id: customerList[0].customer_id,
        customer_name: customerList[0].customer_name,
        metal_id: metalList[0].metal_id,
        metal_description: metalList[0].metal_description,
        weight: 50.000,
        purity_id: purityList[0].purity_id,
        purity_name: purityList[0].purity_name,
        rate: 6500.00,
        piece_count: 1,
        net_weight: 50.000,
        comments: 'Raw gold received from supplier'
      });

      // Sample Metal Outward
      await MetalOutward.create({
        outward_date: '2024-01-18',
        customer_id: customerList[1].customer_id,
        customer_name: customerList[1].customer_name,
        metal_id: metalList[1].metal_id,
        metal_description: metalList[1].metal_description,
        weight: 30.000,
        purity_id: purityList[1].purity_id,
        purity_name: purityList[1].purity_name,
        rate: 5200.00,
        piece_count: 1,
        net_weight: 30.000,
        comments: 'Gold sent for jewelry making'
      });
    }

    console.log('Initial data seeded successfully!');
  } catch (error) {
    console.error('Error seeding data:', error);
  }
};
