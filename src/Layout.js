import React, { useState, useEffect } from "react";
import { Link, useLocation } from "react-router-dom";
import { createPageUrl } from "./utils";
import {
  Home,
  Database,
  ArrowRightLeft,
  History,
  LogOut,
  Users,
  Package,
  UserCheck,
  Coins,
  Zap,
  ChevronDown,
  ChevronRight,
  Menu,
  X,
  Gem,
  Loader2
} from "lucide-react";
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarHeader,
  SidebarFooter,
  SidebarProvider,
  SidebarTrigger,
} from "./components/ui/sidebar";
import { Button } from "./components/ui/button";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "./components/ui/collapsible";
import { User } from "./entities/User";

const navigationItems = [
  {
    title: "Home",
    url: createPageUrl("Home"),
    icon: Home,
  },
  {
    title: "Masters",
    icon: Database,
    children: [
      {
        title: "Customer Master",
        url: createPageUrl("CustomerMaster"),
        icon: Users,
      },
      {
        title: "Item Master",
        url: createPageUrl("ItemMaster"),
        icon: Package,
      },
      {
        title: "Workers Master",
        url: createPageUrl("WorkersMaster"),
        icon: UserCheck,
      },
      {
        title: "Metal Master",
        url: createPageUrl("MetalMaster"),
        icon: Coins,
      },
      {
        title: "Purity Master",
        url: createPageUrl("PurityMaster"),
        icon: Zap,
      },
    ],
  },
  {
    title: "Transactions",
    icon: ArrowRightLeft,
    children: [
      {
        title: "Item Inward",
        url: createPageUrl("ItemInward"),
        icon: Package,
      },
      {
        title: "Item Outward",
        url: createPageUrl("ItemOutward"),
        icon: Package,
      },
      {
        title: "Metal Inward",
        url: createPageUrl("MetalInward"),
        icon: Coins,
      },
      {
        title: "Metal Outward",
        url: createPageUrl("MetalOutward"),
        icon: Coins,
      },
      {
        title: "Sales",
        url: createPageUrl("Sales"),
        icon: Gem,
      },
    ],
  },
  {
    title: "History",
    url: createPageUrl("History"),
    icon: History,
  },
];

export default function Layout({ children, currentPageName }) {
  const location = useLocation();
  const [openItems, setOpenItems] = useState({});
  const [user, setUser] = useState(null);
  const [isLoadingAuth, setIsLoadingAuth] = useState(true);

  useEffect(() => {
    const checkAuth = async () => {
        try {
            const currentUser = await User.me();
            setUser(currentUser);
        } catch (error) {
            setUser(null);
        } finally {
            setIsLoadingAuth(false);
        }
    };
    checkAuth();
  }, []);

  const toggleItem = (itemTitle) => {
    setOpenItems(prev => ({
      ...prev,
      [itemTitle]: !prev[itemTitle]
    }));
  };

  const [loginForm, setLoginForm] = useState({ username: '', password: '' });
  const [loginError, setLoginError] = useState('');
  const [isLoggingIn, setIsLoggingIn] = useState(false);

  const handleLogin = async (e) => {
    e.preventDefault();
    setIsLoggingIn(true);
    setLoginError('');

    try {
      await User.login(loginForm.username, loginForm.password);
      const currentUser = await User.me();
      setUser(currentUser);
    } catch (error) {
      setLoginError(error.message);
    } finally {
      setIsLoggingIn(false);
    }
  };

  const handleLogout = async () => {
    await User.logout();
    setUser(null);
    setIsLoadingAuth(true); // show loader while redirecting
    window.location.reload(); // force reload to clear state and re-check auth
  };

  if (isLoadingAuth) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-slate-900">
        <div className="flex flex-col items-center text-white">
          <Loader2 className="w-10 h-10 animate-spin text-amber-500 mb-4" />
          <p>Loading Jewelry Pro...</p>
        </div>
      </div>
    );
  }

  // Show login page if user is not authenticated
  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-amber-900 flex items-center justify-center p-4">
        <div className="absolute inset-0 bg-gradient-to-br from-slate-900/80 to-amber-900/80"></div>
        
        <div className="w-full max-w-md relative z-10 bg-white/95 backdrop-blur-sm rounded-2xl shadow-2xl p-8 text-center">
          <div className="w-16 h-16 bg-gradient-to-br from-amber-400 to-amber-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
            <Gem className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-slate-800 mb-2">Jewelry Pro</h1>
          <p className="text-slate-600 mb-8">Inventory Management System</p>
          
          <Button 
            onClick={handleLogin}
            className="w-full h-12 bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white font-medium text-base shadow-lg transition-all duration-200"
          >
            Sign In to Continue
          </Button>
          
          <p className="text-sm text-slate-500 mt-6">
            Secure access to your jewelry inventory
          </p>
        </div>
      </div>
    );
  }

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-gradient-to-br from-slate-50 to-slate-100">
        <Sidebar className="border-r border-slate-200 bg-white shadow-xl">
          <SidebarHeader className="border-b border-slate-200 p-6 bg-gradient-to-r from-slate-900 to-slate-800">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-br from-amber-400 to-amber-600 rounded-xl flex items-center justify-center shadow-lg">
                <Gem className="w-6 h-6 text-white" />
              </div>
              <div>
                <h2 className="font-bold text-white text-lg">Jewelry Pro</h2>
                <p className="text-xs text-slate-300">Inventory Management</p>
              </div>
            </div>
          </SidebarHeader>
          
          <SidebarContent className="p-3">
            <SidebarGroup>
              <SidebarGroupContent>
                <SidebarMenu className="space-y-1">
                  {navigationItems.map((item) => (
                    <SidebarMenuItem key={item.title}>
                      {item.children ? (
                        <Collapsible 
                          open={openItems[item.title]} 
                          onOpenChange={() => toggleItem(item.title)}
                        >
                          <CollapsibleTrigger asChild>
                            <SidebarMenuButton className="hover:bg-amber-50 hover:text-amber-700 transition-all duration-200 rounded-xl font-medium group">
                              <item.icon className="w-5 h-5 group-hover:text-amber-600" />
                              <span>{item.title}</span>
                              {openItems[item.title] ? (
                                <ChevronDown className="w-4 h-4 ml-auto" />
                              ) : (
                                <ChevronRight className="w-4 h-4 ml-auto" />
                              )}
                            </SidebarMenuButton>
                          </CollapsibleTrigger>
                          <CollapsibleContent className="mt-1">
                            <div className="ml-4 space-y-1 border-l-2 border-slate-100 pl-4">
                              {item.children.map((child) => (
                                <SidebarMenuButton
                                  key={child.title}
                                  asChild
                                  className={`hover:bg-amber-50 hover:text-amber-700 transition-all duration-200 rounded-lg text-sm ${
                                    location.pathname === child.url ? 'bg-amber-100 text-amber-700 border-l-3 border-amber-500' : ''
                                  }`}
                                >
                                  <Link to={child.url} className="flex items-center gap-3 px-3 py-2">
                                    <child.icon className="w-4 h-4" />
                                    <span>{child.title}</span>
                                  </Link>
                                </SidebarMenuButton>
                              ))}
                            </div>
                          </CollapsibleContent>
                        </Collapsible>
                      ) : (
                        <SidebarMenuButton 
                          asChild 
                          className={`hover:bg-amber-50 hover:text-amber-700 transition-all duration-200 rounded-xl font-medium ${
                            location.pathname === item.url ? 'bg-amber-100 text-amber-700 border-l-3 border-amber-500' : ''
                          }`}
                        >
                          <Link to={item.url} className="flex items-center gap-3 px-4 py-3">
                            <item.icon className="w-5 h-5" />
                            <span>{item.title}</span>
                          </Link>
                        </SidebarMenuButton>
                      )}
                    </SidebarMenuItem>
                  ))}
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>
          </SidebarContent>

          <SidebarFooter className="border-t border-slate-200 p-4 bg-slate-50">
            <div className="mb-3 px-2">
              <p className="text-sm font-medium text-slate-700 truncate">{user?.full_name || 'User'}</p>
              <p className="text-xs text-slate-500 truncate">{user?.email}</p>
            </div>
            <Button
              onClick={handleLogout}
              variant="ghost"
              className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50 transition-all duration-200"
            >
              <LogOut className="w-4 h-4 mr-3" />
              Logout
            </Button>
          </SidebarFooter>
        </Sidebar>

        <main className="flex-1 flex flex-col overflow-hidden">
          <header className="bg-white border-b border-slate-200 px-6 py-4 shadow-sm md:hidden">
            <div className="flex items-center gap-4">
              <SidebarTrigger className="hover:bg-slate-100 p-2 rounded-lg transition-all duration-200" />
              <h1 className="text-xl font-semibold text-slate-800">Jewelry Pro</h1>
            </div>
          </header>

          <div className="flex-1 overflow-auto bg-gradient-to-br from-slate-50 to-slate-100">
            {children}
          </div>
        </main>
      </div>
      
      <style jsx>{`
        .border-l-3 {
          border-left-width: 3px;
        }
      `}</style>
    </SidebarProvider>
  );
}