import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import Layout from './Layout.jsx';

// Import all pages
import Home from './pages/Home';
import CustomerMaster from './pages/CustomerMaster';
import ItemMaster from './pages/ItemMaster';
import WorkersMaster from './pages/WorkersMaster';
import MetalMaster from './pages/MetalMaster';
import PurityMaster from './pages/PurityMaster';
import ItemInward from './pages/ItemInward';
import ItemOutward from './pages/ItemOutward';
import MetalInward from './pages/MetalInward';
import MetalOutward from './pages/MetalOutward';
import Sales from './pages/Sales';
import History from './pages/History';

function App() {
  return (
    <Router>
      <Layout>
        <Routes>
          <Route path="/" element={<Navigate to="/home" replace />} />
          <Route path="/home" element={<Home />} />
          
          {/* Masters Routes */}
          <Route path="/customer-master" element={<CustomerMaster />} />
          <Route path="/item-master" element={<ItemMaster />} />
          <Route path="/workers-master" element={<WorkersMaster />} />
          <Route path="/metal-master" element={<MetalMaster />} />
          <Route path="/purity-master" element={<PurityMaster />} />
          
          {/* Transactions Routes */}
          <Route path="/item-inward" element={<ItemInward />} />
          <Route path="/item-outward" element={<ItemOutward />} />
          <Route path="/metal-inward" element={<MetalInward />} />
          <Route path="/metal-outward" element={<MetalOutward />} />
          <Route path="/sales" element={<Sales />} />
          
          {/* History Route */}
          <Route path="/history" element={<History />} />
          
          {/* Catch all route */}
          <Route path="*" element={<Navigate to="/home" replace />} />
        </Routes>
      </Layout>
    </Router>
  );
}

export default App;
