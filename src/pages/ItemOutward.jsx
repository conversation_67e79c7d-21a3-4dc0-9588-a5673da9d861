import React from 'react';
import MasterPage from '@/components/masters/MasterPage';
import { ItemOutward } from '@/entities/ItemOutward';
import ItemOutwardForm from '../components/transactions/ItemOutwardForm';

const columns = [
  { accessorKey: 'customer_name', header: 'Customer', sortable: true },
  { accessorKey: 'item_description', header: 'Item', sortable: true },
  { accessorKey: 'net_weight', header: 'Net Weight (g)', sortable: true },
  { accessorKey: 'piece_count', header: 'Pieces', sortable: true },
];

export default function ItemOutwardPage() {
  return (
    <MasterPage
      entity={ItemOutward}
      entityName="Item Outward"
      entityNamePlural="Item Outwards"
      columns={columns}
      FormComponent={ItemOutwardForm}
    />
  );
}