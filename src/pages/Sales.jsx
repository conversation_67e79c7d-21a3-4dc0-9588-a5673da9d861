import React from 'react';
import MasterPage from '@/components/masters/MasterPage';
import { Sale } from '@/entities/Sale';
import SalesForm from '../components/transactions/SalesForm';

const columns = [
  { accessorKey: 'customer_name', header: 'Customer', sortable: true },
  { accessorKey: 'item_description', header: 'Item', sortable: true },
  { accessorKey: 'net_weight', header: 'Net Weight (g)', sortable: true },
  { accessorKey: 'piece_count', header: 'Pieces', sortable: true },
];

export default function SalesPage() {
  return (
    <MasterPage
      entity={Sale}
      entityName="Sale"
      entityNamePlural="Sales"
      columns={columns}
      FormComponent={SalesForm}
    />
  );
}