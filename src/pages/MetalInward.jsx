import React from 'react';
import MasterPage from '@/components/masters/MasterPage';
import { MetalInward } from '@/entities/MetalInward';
import MetalInwardForm from '../components/transactions/MetalInwardForm';

const columns = [
  { accessorKey: 'customer_name', header: 'Customer', sortable: true },
  { accessorKey: 'item_description', header: 'Metal Type', sortable: true },
  { accessorKey: 'weight', header: 'Weight (g)', sortable: true },
  { accessorKey: 'purity', header: 'Purity', sortable: true },
  { accessorKey: 'rate', header: 'Rate', sortable: true, cell: ({row}) => `₹${row.rate?.toFixed(2)}` },
];

export default function MetalInwardPage() {
  return (
    <MasterPage
      entity={MetalInward}
      entityName="Metal Inward"
      entityNamePlural="Metal Inwards"
      columns={columns}
      FormComponent={MetalInwardForm}
    />
  );
}