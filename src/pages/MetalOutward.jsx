import React from 'react';
import MasterPage from '@/components/masters/MasterPage';
import { MetalOutward } from '@/entities/MetalOutward';
import MetalOutwardForm from '../components/transactions/MetalOutwardForm';

const columns = [
  { accessorKey: 'customer_name', header: 'Customer', sortable: true },
  { accessorKey: 'item_description', header: 'Metal Type', sortable: true },
  { accessorKey: 'weight', header: 'Weight (g)', sortable: true },
  { accessorKey: 'purity', header: 'Purity', sortable: true },
  { accessorKey: 'rate', header: 'Rate', sortable: true, cell: ({row}) => `₹${row.rate?.toFixed(2)}` },
];

export default function MetalOutwardPage() {
  return (
    <MasterPage
      entity={MetalOutward}
      entityName="Metal Outward"
      entityNamePlural="Metal Outwards"
      columns={columns}
      FormComponent={MetalOutwardForm}
    />
  );
}