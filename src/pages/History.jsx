import React, { useState } from 'react';
import PageHeader from '@/components/common/PageHeader';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import HistoryReport from '@/components/history/HistoryReport';

import { ItemInward } from '@/entities/ItemInward';
import { ItemOutward } from '@/entities/ItemOutward';
import { MetalInward } from '@/entities/MetalInward';
import { MetalOutward } from '@/entities/MetalOutward';
import { Sale } from '@/entities/Sale';

const reportTabs = [
  { value: 'itemInward', label: 'Item Inward', entity: ItemInward, columns: [
      { accessorKey: 'customer_name', header: 'Customer' },
      { accessorKey: 'item_description', header: 'Item' },
      { accessorKey: 'net_weight', header: 'Net Weight (g)' },
  ]},
  { value: 'itemOutward', label: 'Item Outward', entity: ItemOutward, columns: [
      { accessorKey: 'customer_name', header: 'Customer' },
      { accessorKey: 'item_description', header: 'Item' },
      { accessorKey: 'net_weight', header: 'Net Weight (g)' },
  ]},
  { value: 'metalInward', label: 'Metal Inward', entity: MetalInward, columns: [
      { accessorKey: 'customer_name', header: 'Customer' },
      { accessorKey: 'item_description', header: 'Item' },
      { accessorKey: 'weight', header: 'Weight (g)' },
      { accessorKey: 'purity', header: 'Purity' },
  ]},
  { value: 'metalOutward', label: 'Metal Outward', entity: MetalOutward, columns: [
      { accessorKey: 'customer_name', header: 'Customer' },
      { accessorKey: 'item_description', header: 'Item' },
      { accessorKey: 'weight', header: 'Weight (g)' },
      { accessorKey: 'purity', header: 'Purity' },
  ]},
  { value: 'sales', label: 'Sales', entity: Sale, columns: [
      { accessorKey: 'customer_name', header: 'Customer' },
      { accessorKey: 'item_description', header: 'Item' },
      { accessorKey: 'net_weight', header: 'Net Weight (g)' },
  ]},
];

export default function HistoryPage() {
  return (
    <div className="p-6 space-y-6">
      <PageHeader
        title="Transaction History"
        description="Review and filter past transactions."
      />
      <Tabs defaultValue="itemInward" className="w-full">
        <TabsList className="grid w-full grid-cols-2 md:grid-cols-5">
          {reportTabs.map(tab => (
            <TabsTrigger key={tab.value} value={tab.value}>{tab.label}</TabsTrigger>
          ))}
        </TabsList>
        {reportTabs.map(tab => (
          <TabsContent key={tab.value} value={tab.value}>
            <HistoryReport
              entity={tab.entity}
              entityName={tab.label}
              columns={tab.columns}
            />
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
}