import React from 'react';
import MasterPage from '@/components/masters/MasterPage';
import { Worker } from '@/entities/Worker';
import { Badge } from '@/components/ui/badge';
import WorkerForm from '../components/masters/WorkerForm';

const columns = [
  { accessorKey: 'worker_name', header: 'Name', sortable: true },
  { accessorKey: 'balance', header: 'Balance', sortable: true, cell: ({row}) => `₹${row.balance?.toFixed(2)}` },
  { accessorKey: 'remarks', header: 'Remarks' },
  { 
    accessorKey: 'status', 
    header: 'Status', 
    sortable: true,
    cell: ({ row }) => (
      <Badge className={row.status === 'active' ? 'bg-emerald-100 text-emerald-800' : 'bg-slate-100 text-slate-800'}>
        {row.status}
      </Badge>
    )
  },
];

export default function WorkersMaster() {
  return (
    <MasterPage
      entity={Worker}
      entityName="Worker"
      entityNamePlural="Workers"
      columns={columns}
      FormComponent={WorkerForm}
    />
  );
}