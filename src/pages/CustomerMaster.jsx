import React from 'react';
import MasterPage from '../components/masters/MasterPage';
import { Customer } from '../entities/Customer';
import { Badge } from '../components/ui/badge';
import CustomerForm from '../components/masters/CustomerForm';

const columns = [
  { accessorKey: 'customer_name', header: 'Name', sortable: true },
  { accessorKey: 'customer_balance', header: 'Balance', sortable: true, cell: ({row}) => `₹${row.customer_balance?.toFixed(2)}` },
  { accessorKey: 'mobile_no', header: 'Mobile' },
  { accessorKey: 'email', header: 'Email' },
  { 
    accessorKey: 'status', 
    header: 'Status', 
    sortable: true,
    cell: ({ row }) => (
      <Badge className={row.status === 'active' ? 'bg-emerald-100 text-emerald-800' : 'bg-slate-100 text-slate-800'}>
        {row.status}
      </Badge>
    )
  },
];

export default function CustomerMaster() {
  return (
    <MasterPage
      entity={Customer}
      entityName="Customer"
      entityNamePlural="Customers"
      columns={columns}
      FormComponent={CustomerForm}
    />
  );
}