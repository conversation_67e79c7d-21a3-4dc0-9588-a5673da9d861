import React from 'react';
import MasterPage from '@/components/masters/MasterPage';
import { Item } from '@/entities/Item';
import { Badge } from '@/components/ui/badge';
import ItemForm from '../components/masters/ItemForm';

const columns = [
  { accessorKey: 'item_number', header: 'Item Number', sortable: true },
  { accessorKey: 'item_description', header: 'Description', sortable: true },
  { accessorKey: 'remarks', header: 'Remarks' },
  { 
    accessorKey: 'status', 
    header: 'Status', 
    sortable: true,
    cell: ({ row }) => (
      <Badge className={row.status === 'active' ? 'bg-emerald-100 text-emerald-800' : 'bg-slate-100 text-slate-800'}>
        {row.status}
      </Badge>
    )
  },
];

export default function ItemMaster() {
  return (
    <MasterPage
      entity={Item}
      entityName="Item"
      entityNamePlural="Items"
      columns={columns}
      FormComponent={ItemForm}
    />
  );
}