import React from 'react';
import MasterPage from '../components/masters/MasterPage';
import { Metal } from '../entities/Metal';
import { Badge } from '../components/ui/badge';
import MetalForm from '../components/masters/MetalForm';

const columns = [
  { accessorKey: 'metal_description', header: 'Description', sortable: true },
  { accessorKey: 'remarks', header: 'Remarks' },
  { 
    accessorKey: 'status', 
    header: 'Status', 
    sortable: true,
    cell: ({ row }) => (
      <Badge className={row.status === 'active' ? 'bg-emerald-100 text-emerald-800' : 'bg-slate-100 text-slate-800'}>
        {row.status}
      </Badge>
    )
  },
];

export default function MetalMaster() {
  return (
    <MasterPage
      entity={Metal}
      entityName="Metal"
      entityNamePlural="Metals"
      columns={columns}
      FormComponent={MetalForm}
    />
  );
}