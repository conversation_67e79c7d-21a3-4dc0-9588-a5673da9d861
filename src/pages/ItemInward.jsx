import React from 'react';
import MasterPage from '@/components/masters/MasterPage';
import { ItemInward } from '@/entities/ItemInward';
import ItemInwardForm from '../components/transactions/ItemInwardForm';

const columns = [
  { accessorKey: 'customer_name', header: 'Customer', sortable: true },
  { accessorKey: 'item_description', header: 'Item', sortable: true },
  { accessorKey: 'net_weight', header: 'Net Weight (g)', sortable: true },
  { accessorKey: 'piece_count', header: 'Pieces', sortable: true },
];

export default function ItemInwardPage() {
  return (
    <MasterPage
      entity={ItemInward}
      entityName="Item Inward"
      entityNamePlural="Item Inwards"
      columns={columns}
      FormComponent={ItemInwardForm}
    />
  );
}