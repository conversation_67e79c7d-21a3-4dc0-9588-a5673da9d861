{"hash": "cb6aaddb", "browserHash": "1083d47b", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "ccca0e4c", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "7ed1d3da", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "f4cf660c", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "2d33f8e9", "needsInterop": true}, "clsx": {"src": "../../clsx/dist/clsx.m.js", "file": "clsx.js", "fileHash": "5461ea71", "needsInterop": false}, "date-fns": {"src": "../../date-fns/esm/index.js", "file": "date-fns.js", "fileHash": "b497f1e2", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.mjs", "file": "lucide-react.js", "fileHash": "be2faa8d", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "e38ca058", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "9ddecb26", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/tailwind-merge.mjs", "file": "tailwind-merge.js", "fileHash": "3a4ef136", "needsInterop": false}}, "chunks": {"chunk-WALXKXZM": {"file": "chunk-WALXKXZM.js"}, "chunk-WQMOH32Y": {"file": "chunk-WQMOH32Y.js"}, "chunk-5WWUZCGV": {"file": "chunk-5WWUZCGV.js"}}}