{"name": "<PERSON><PERSON>", "type": "object", "properties": {"item_id": {"type": "string", "description": "Auto-generated item ID"}, "item_number": {"type": "string", "description": "Item number"}, "item_description": {"type": "string", "description": "Item description"}, "remarks": {"type": "string", "description": "Additional remarks"}, "status": {"type": "string", "enum": ["active", "inactive"], "default": "active", "description": "Item status"}}, "required": ["item_number", "item_description"]}