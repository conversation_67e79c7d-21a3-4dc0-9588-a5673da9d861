{"name": "ItemOutward", "type": "object", "properties": {"item_outward_id": {"type": "string", "description": "Auto-generated item outward ID"}, "outward_date": {"type": "string", "format": "date", "description": "Outward date"}, "customer_id": {"type": "string", "description": "Customer ID reference"}, "customer_name": {"type": "string", "description": "Customer name"}, "item_id": {"type": "string", "description": "Item ID reference"}, "item_description": {"type": "string", "description": "Item description"}, "melting": {"type": "string", "description": "Melting details"}, "gross_weight": {"type": "number", "description": "Gross weight"}, "st": {"type": "string", "description": "ST value"}, "en": {"type": "string", "description": "EN value"}, "thd": {"type": "string", "description": "THD value"}, "piece_count": {"type": "number", "description": "Number of pieces"}, "net_weight": {"type": "number", "description": "Net weight"}, "comments": {"type": "string", "description": "Additional comments"}}, "required": ["outward_date", "customer_id", "item_id"]}