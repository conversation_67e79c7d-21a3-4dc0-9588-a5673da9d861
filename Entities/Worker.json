{"name": "Worker", "type": "object", "properties": {"worker_id": {"type": "string", "description": "Auto-generated worker ID"}, "worker_name": {"type": "string", "description": "Worker name"}, "balance": {"type": "number", "description": "Worker balance amount"}, "remarks": {"type": "string", "description": "Additional remarks"}, "status": {"type": "string", "enum": ["active", "inactive"], "default": "active", "description": "Worker status"}}, "required": ["worker_name"]}