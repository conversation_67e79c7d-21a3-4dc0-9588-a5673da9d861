{"name": "Metal", "type": "object", "properties": {"metal_id": {"type": "string", "description": "Auto-generated metal ID"}, "metal_description": {"type": "string", "description": "Metal description"}, "remarks": {"type": "string", "description": "Additional remarks"}, "status": {"type": "string", "enum": ["active", "inactive"], "default": "active", "description": "Metal status"}}, "required": ["metal_description"]}