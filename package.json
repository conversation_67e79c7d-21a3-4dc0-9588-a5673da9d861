{"name": "jewelry-inventory-system", "version": "1.0.0", "description": "Complete Jewelry Inventory Management System", "type": "module", "main": "index.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "start": "vite"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "lucide-react": "^0.263.1", "date-fns": "^2.29.3", "clsx": "^1.2.1", "tailwind-merge": "^1.14.0"}, "devDependencies": {"@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@vitejs/plugin-react": "^4.0.0", "autoprefixer": "^10.4.14", "postcss": "^8.4.24", "tailwindcss": "^3.3.0", "vite": "^4.3.2"}, "keywords": ["jewelry", "inventory", "management", "react", "vite"], "author": "Jewelry Pro", "license": "MIT"}