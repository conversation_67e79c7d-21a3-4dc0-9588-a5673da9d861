import React, { useState, useEffect } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

export default function CustomerForm({ data, onSubmit, onCancel }) {
  const [formData, setFormData] = useState({
    customer_name: '',
    customer_balance: 0,
    mobile_no: '',
    email: '',
    address: '',
    remarks: '',
    status: 'active',
  });

  useEffect(() => {
    if (data) {
      setFormData(data);
    }
  }, [data]);

  const handleChange = (e) => {
    const { id, value } = e.target;
    setFormData(prev => ({ ...prev, [id]: value }));
  };
  
  const handleSelectChange = (value) => {
    setFormData(prev => ({ ...prev, status: value }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <Label htmlFor="customer_name">Customer Name</Label>
        <Input id="customer_name" value={formData.customer_name} onChange={handleChange} required />
      </div>
      <div>
        <Label htmlFor="customer_balance">Balance</Label>
        <Input id="customer_balance" type="number" value={formData.customer_balance} onChange={handleChange} />
      </div>
      <div>
        <Label htmlFor="mobile_no">Mobile No.</Label>
        <Input id="mobile_no" type="tel" value={formData.mobile_no} onChange={handleChange} />
      </div>
      <div>
        <Label htmlFor="email">Email</Label>
        <Input id="email" type="email" value={formData.email} onChange={handleChange} />
      </div>
      <div>
        <Label htmlFor="address">Address</Label>
        <Textarea id="address" value={formData.address} onChange={handleChange} />
      </div>
      <div>
        <Label htmlFor="remarks">Remarks</Label>
        <Textarea id="remarks" value={formData.remarks} onChange={handleChange} />
      </div>
      <div>
        <Label htmlFor="status">Status</Label>
        <Select value={formData.status} onValueChange={handleSelectChange}>
          <SelectTrigger>
            <SelectValue placeholder="Select status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="active">Active</SelectItem>
            <SelectItem value="inactive">Inactive</SelectItem>
          </SelectContent>
        </Select>
      </div>
      <div className="flex justify-end gap-2">
        <Button type="button" variant="outline" onClick={onCancel}>Cancel</Button>
        <Button type="submit">Save</Button>
      </div>
    </form>
  );
}