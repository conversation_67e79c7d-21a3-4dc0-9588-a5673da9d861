import React, { useState, useEffect, useMemo } from 'react';
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Plus, Upload, Search } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import PageHeader from '../common/PageHeader';
import DataTable from '../common/DataTable';
import { format } from 'date-fns';

export default function MasterPage({ 
  entity, 
  entityName,
  entityNamePlural,
  columns,
  FormComponent,
  pageIcon: PageIcon,
}) {
  const [data, setData] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [sortConfig, setSortConfig] = useState({ key: 'created_date', direction: 'descending' });
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState(null);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setIsLoading(true);
    const records = await entity.list();
    setData(records);
    setIsLoading(false);
  };
  
  const handleSort = (key) => {
    let direction = 'ascending';
    if (sortConfig.key === key && sortConfig.direction === 'ascending') {
      direction = 'descending';
    }
    setSortConfig({ key, direction });
  };
  
  const sortedData = useMemo(() => {
    let sortableItems = [...data];
    if (sortConfig.key !== null) {
      sortableItems.sort((a, b) => {
        if (a[sortConfig.key] < b[sortConfig.key]) {
          return sortConfig.direction === 'ascending' ? -1 : 1;
        }
        if (a[sortConfig.key] > b[sortConfig.key]) {
          return sortConfig.direction === 'ascending' ? 1 : -1;
        }
        return 0;
      });
    }
    return sortableItems;
  }, [data, sortConfig]);


  const filteredData = sortedData.filter(item =>
    Object.values(item).some(val =>
      String(val).toLowerCase().includes(searchTerm.toLowerCase())
    )
  );

  const handleEdit = (record) => {
    setSelectedRecord(record);
    setIsFormOpen(true);
  };

  const handleDelete = async (id) => {
    if (window.confirm(`Are you sure you want to delete this ${entityName}?`)) {
      await entity.delete(id);
      loadData();
    }
  };

  const handleFormSubmit = async (formData) => {
    if (selectedRecord) {
      await entity.update(selectedRecord.id, formData);
    } else {
      await entity.create(formData);
    }
    setIsFormOpen(false);
    setSelectedRecord(null);
    loadData();
  };

  const exportToCSV = () => {
    const headers = columns.map(c => c.header).join(',');
    const rows = filteredData.map(row => 
      columns.map(col => {
        const value = col.cell ? col.cell({ row }) : row[col.accessorKey];
        return `"${String(value || '').replace(/"/g, '""')}"`;
      }).join(',')
    ).join('\n');
    
    const csvContent = `data:text/csv;charset=utf-8,${headers}\n${rows}`;
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", `${entityNamePlural.toLowerCase()}_export.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const actions = [
    <Button key="export" variant="outline" onClick={exportToCSV}>
      <Upload className="mr-2 h-4 w-4" /> Export
    </Button>,
    <Button key="add" onClick={() => { setSelectedRecord(null); setIsFormOpen(true); }}>
      <Plus className="mr-2 h-4 w-4" /> Add New {entityName}
    </Button>
  ];

  const tableColumns = [
    ...columns,
    {
      accessorKey: 'created_date',
      header: 'Created On',
      sortable: true,
      cell: ({ row }) => format(new Date(row.created_date), 'MMM d, yyyy')
    },
    {
      accessorKey: 'actions',
      header: 'Actions',
      cell: ({ row }) => (
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={() => handleEdit(row)}>Edit</Button>
          <Button variant="destructive" size="sm" onClick={() => handleDelete(row.id)}>Delete</Button>
        </div>
      ),
    },
  ];

  return (
    <div className="p-6 space-y-6">
      <PageHeader 
        title={entityNamePlural}
        description={`Manage your ${entityNamePlural.toLowerCase()}.`}
        actions={actions}
      />
      <div className="bg-white/60 backdrop-blur-sm p-4 rounded-xl shadow-lg border border-slate-200">
        <div className="relative">
          <Search className="absolute left-3 top-3 h-4 w-4 text-slate-500" />
          <Input
            placeholder={`Search ${entityNamePlural.toLowerCase()}...`}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>
      <DataTable
        columns={tableColumns}
        data={filteredData}
        onSort={handleSort}
      />

      <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{selectedRecord ? `Edit ${entityName}` : `Add New ${entityName}`}</DialogTitle>
          </DialogHeader>
          <FormComponent
            data={selectedRecord}
            onSubmit={handleFormSubmit}
            onCancel={() => setIsFormOpen(false)}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}