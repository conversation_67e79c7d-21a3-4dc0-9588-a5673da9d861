import React, { useState, useEffect, useMemo } from 'react';
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Upload, Search } from 'lucide-react';
import DataTable from '../common/DataTable';
import { format, subDays } from 'date-fns';

export default function HistoryReport({ entity, entityName, columns }) {
  const [data, setData] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [sortConfig, setSortConfig] = useState({ key: 'created_date', direction: 'descending' });
  const [dateRange, setDateRange] = useState({
    from: format(subDays(new Date(), 30), 'yyyy-MM-dd'),
    to: format(new Date(), 'yyyy-MM-dd'),
  });

  useEffect(() => {
    loadData();
  }, [dateRange]);

  const loadData = async () => {
    setIsLoading(true);
    const records = await entity.list();
    const filteredByDate = records.filter(r => {
      const recordDate = new Date(r.created_date);
      const fromDate = new Date(dateRange.from);
      const toDate = new Date(dateRange.to);
      return recordDate >= fromDate && recordDate <= toDate;
    });
    setData(filteredByDate);
    setIsLoading(false);
  };
  
  const handleSort = (key) => {
    let direction = 'ascending';
    if (sortConfig.key === key && sortConfig.direction === 'ascending') {
      direction = 'descending';
    }
    setSortConfig({ key, direction });
  };
  
  const sortedData = useMemo(() => {
    let sortableItems = [...data];
    if (sortConfig.key !== null) {
      sortableItems.sort((a, b) => {
        if (a[sortConfig.key] < b[sortConfig.key]) {
          return sortConfig.direction === 'ascending' ? -1 : 1;
        }
        if (a[sortConfig.key] > b[sortConfig.key]) {
          return sortConfig.direction === 'ascending' ? 1 : -1;
        }
        return 0;
      });
    }
    return sortableItems;
  }, [data, sortConfig]);

  const filteredData = sortedData.filter(item =>
    Object.values(item).some(val =>
      String(val).toLowerCase().includes(searchTerm.toLowerCase())
    )
  );

  const handleDateChange = (field, value) => {
    setDateRange(prev => ({ ...prev, [field]: value }));
  };

  const exportToCSV = () => {
    const headers = columns.map(c => c.header).join(',');
    const rows = filteredData.map(row => 
      columns.map(col => `"${String(row[col.accessorKey] || '').replace(/"/g, '""')}"`).join(',')
    ).join('\n');
    
    const csvContent = `data:text/csv;charset=utf-8,${headers}\n${rows}`;
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", `${entityName.toLowerCase()}_report.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const tableColumns = [
    ...columns,
    {
      accessorKey: 'created_date',
      header: 'Date',
      sortable: true,
      cell: ({ row }) => format(new Date(row.created_date), 'MMM d, yyyy')
    },
  ];

  return (
    <div className="space-y-4 pt-4">
      <div className="bg-white/60 backdrop-blur-sm p-4 rounded-xl shadow-lg border border-slate-200">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-slate-500" />
            <Input
              placeholder={`Search ${entityName.toLowerCase()}...`}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <div>
            <Label htmlFor="fromDate" className="text-sm font-medium mb-1 block">From Date</Label>
            <Input
              id="fromDate"
              type="date"
              value={dateRange.from}
              onChange={(e) => handleDateChange('from', e.target.value)}
            />
          </div>
          <div>
            <Label htmlFor="toDate" className="text-sm font-medium mb-1 block">To Date</Label>
            <Input
              id="toDate"
              type="date"
              value={dateRange.to}
              onChange={(e) => handleDateChange('to', e.target.value)}
            />
          </div>
          <div className="flex items-end">
            <Button variant="outline" onClick={exportToCSV} className="w-full">
              <Upload className="mr-2 h-4 w-4" /> Export
            </Button>
          </div>
        </div>
      </div>
      <DataTable
        columns={tableColumns}
        data={filteredData}
        onSort={handleSort}
      />
    </div>
  );
}