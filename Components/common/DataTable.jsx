import React from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { ArrowUpDown } from 'lucide-react';
import { Button } from '@/components/ui/button';

export default function DataTable({ columns, data, onSort }) {
  return (
    <div className="rounded-xl border border-slate-200 bg-white/60 backdrop-blur-sm overflow-hidden shadow-lg">
      <Table>
        <TableHeader className="bg-slate-50">
          <TableRow>
            {columns.map((column) => (
              <TableHead key={column.accessorKey}>
                {column.sortable ? (
                  <Button variant="ghost" onClick={() => onSort(column.accessorKey)}>
                    {column.header}
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                  </Button>
                ) : (
                  column.header
                )}
              </TableHead>
            ))}
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.length > 0 ? (
            data.map((row) => (
              <TableRow key={row.id} className="hover:bg-amber-50/50 transition-colors">
                {columns.map((column) => (
                  <TableCell key={column.accessorKey}>
                    {column.cell ? column.cell({ row }) : row[column.accessorKey]}
                  </TableCell>
                ))}
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={columns.length} className="h-24 text-center text-slate-500">
                No results found.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
}