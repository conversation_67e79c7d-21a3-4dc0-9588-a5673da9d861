import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Plus, UserPlus, Package, Users } from "lucide-react";

export default function QuickActions({ onAddCustomer, onAddItem, onAddWorker }) {
  const actions = [
    {
      title: "Add New Customer",
      description: "Register a new customer",
      icon: UserPlus,
      onClick: onAddCustomer,
      color: "from-blue-500 to-blue-600"
    },
    {
      title: "Add New Item",
      description: "Add item to inventory",
      icon: Package,
      onClick: onAddItem,
      color: "from-emerald-500 to-emerald-600"
    },
    {
      title: "Add Worker",
      description: "Register new worker",
      icon: Users,
      onClick: onAddWorker,
      color: "from-purple-500 to-purple-600"
    }
  ];

  return (
    <Card className="mb-8 border-0 shadow-lg bg-white/80 backdrop-blur-sm">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-slate-800">
          <Plus className="w-5 h-5" />
          Quick Actions
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {actions.map((action, index) => (
            <Button
              key={index}
              variant="outline"
              className="w-full h-auto p-4 hover:shadow-md transition-all duration-200 border-slate-200 hover:border-slate-300 group"
              onClick={action.onClick}
            >
              <div className="flex items-center gap-3 w-full">
                <div className={`p-2 rounded-lg bg-gradient-to-br ${action.color} bg-opacity-20 group-hover:bg-opacity-30 transition-all duration-200`}>
                  <action.icon className="w-5 h-5 text-slate-700" />
                </div>
                <div className="text-left">
                  <div className="font-medium text-slate-800">{action.title}</div>
                  <div className="text-sm text-slate-500">{action.description}</div>
                </div>
              </div>
            </Button>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}