import React from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Package, Weight, TrendingUp } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";

export default function InventorySummary({ data, isLoading }) {
  const totalInwardWeight = data.itemInwards?.reduce((sum, item) => sum + (item.net_weight || 0), 0) || 0;
  const totalOutwardWeight = data.itemOutwards?.reduce((sum, item) => sum + (item.net_weight || 0), 0) || 0;
  const currentStock = totalInwardWeight - totalOutwardWeight;
  
  const inventoryStats = [
    {
      title: "Current Stock",
      value: `${currentStock.toFixed(2)}g`,
      icon: Package,
      progress: Math.min((currentStock / 1000) * 100, 100)
    },
    {
      title: "Total Inward",
      value: `${totalInwardWeight.toFixed(2)}g`,
      icon: TrendingUp,
      progress: 75
    },
    {
      title: "Total Outward",
      value: `${totalOutwardWeight.toFixed(2)}g`,
      icon: Weight,
      progress: 45
    }
  ];

  return (
    <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-slate-800">
          <Package className="w-5 h-5" />
          Inventory Summary
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {isLoading ? (
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="space-y-2">
                <div className="flex justify-between items-center">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-4 w-16" />
                </div>
                <Skeleton className="h-2 w-full" />
              </div>
            ))}
          </div>
        ) : (
          inventoryStats.map((stat, index) => (
            <div key={index} className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <stat.icon className="w-4 h-4 text-slate-600" />
                  <span className="text-sm font-medium text-slate-700">{stat.title}</span>
                </div>
                <span className="text-sm font-semibold text-slate-800">{stat.value}</span>
              </div>
              <Progress value={stat.progress} className="h-2" />
            </div>
          ))
        )}
        
        <div className="pt-4 border-t border-slate-200">
          <div className="text-sm text-slate-600 mb-2">Recent Activity</div>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-slate-500">Items In (Today)</span>
              <span className="font-medium text-emerald-600">+{data.itemInwards?.length || 0}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-slate-500">Items Out (Today)</span>
              <span className="font-medium text-red-600">-{data.itemOutwards?.length || 0}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-slate-500">Sales (Today)</span>
              <span className="font-medium text-blue-600">{data.sales?.length || 0}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}