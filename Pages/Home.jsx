import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { createPageUrl } from "@/utils";
import { 
  Package, 
  TrendingUp, 
  Users, 
  Coins, 
  ArrowUpRight, 
  ArrowDownRight,
  Plus,
  UserPlus,
  UserCheck,
  Gem,
  Calendar,
  Weight
} from "lucide-react";
import { ItemInward, ItemOutward, MetalInward, MetalOutward, Sale, Customer, Item, Worker } from "@/entities/all";

import DashboardStats from "../components/dashboard/DashboardStats";
import RecentTransactions from "../components/dashboard/RecentTransactions";
import QuickActions from "../components/dashboard/QuickActions";
import InventorySummary from "../components/dashboard/InventorySummary";
import CustomerForm from "../components/masters/CustomerForm";
import ItemForm from "../components/masters/ItemForm";
import WorkerForm from "../components/masters/WorkerForm";

export default function Home() {
  const [dashboardData, setDashboardData] = useState({
    itemInwards: [],
    itemOutwards: [],
    metalInwards: [],
    metalOutwards: [],
    sales: [],
    customers: [],
    items: [],
    workers: []
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isCustomerFormOpen, setIsCustomerFormOpen] = useState(false);
  const [isItemFormOpen, setIsItemFormOpen] = useState(false);
  const [isWorkerFormOpen, setIsWorkerFormOpen] = useState(false);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);
      const [
        itemInwards,
        itemOutwards,
        metalInwards,
        metalOutwards,
        sales,
        customers,
        items,
        workers
      ] = await Promise.all([
        ItemInward.list('-created_date', 5),
        ItemOutward.list('-created_date', 5),
        MetalInward.list('-created_date', 5),
        MetalOutward.list('-created_date', 5),
        Sale.list('-created_date', 5),
        Customer.list(),
        Item.list(),
        Worker.list()
      ]);

      setDashboardData({
        itemInwards,
        itemOutwards,
        metalInwards,
        metalOutwards,
        sales,
        customers,
        items,
        workers
      });
    } catch (error) {
      console.error("Error loading dashboard data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleQuickAdd = async (entity, data, closeForm) => {
    await entity.create(data);
    closeForm(false);
    loadDashboardData();
  };

  return (
    <div className="p-6 space-y-8 bg-gradient-to-br from-slate-50 to-slate-100 min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
          <div>
            <h1 className="text-3xl font-bold text-slate-800 mb-2">Dashboard</h1>
            <p className="text-slate-600">Welcome to your jewelry inventory management system</p>
          </div>
          <div className="text-right">
            <p className="text-sm text-slate-500">Today</p>
            <p className="text-lg font-semibold text-slate-800">
              {new Date().toLocaleDateString('en-US', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })}
            </p>
          </div>
        </div>

        {/* Dashboard Stats */}
        <DashboardStats 
          data={dashboardData}
          isLoading={isLoading}
        />

        {/* Quick Actions */}
        <QuickActions 
          onAddCustomer={() => setIsCustomerFormOpen(true)}
          onAddItem={() => setIsItemFormOpen(true)}
          onAddWorker={() => setIsWorkerFormOpen(true)}
        />

        {/* Main Content Grid */}
        <div className="grid lg:grid-cols-3 gap-8">
          {/* Recent Transactions */}
          <div className="lg:col-span-2">
            <RecentTransactions 
              data={dashboardData}
              isLoading={isLoading}
            />
          </div>

          {/* Inventory Summary */}
          <div>
            <InventorySummary 
              data={dashboardData}
              isLoading={isLoading}
            />
          </div>
        </div>
      </div>

      {/* Quick Add Dialogs */}
      <Dialog open={isCustomerFormOpen} onOpenChange={setIsCustomerFormOpen}>
        <DialogContent>
          <DialogHeader><DialogTitle>Add New Customer</DialogTitle></DialogHeader>
          <CustomerForm onSubmit={(data) => handleQuickAdd(Customer, data, setIsCustomerFormOpen)} onCancel={() => setIsCustomerFormOpen(false)} />
        </DialogContent>
      </Dialog>
      <Dialog open={isItemFormOpen} onOpenChange={setIsItemFormOpen}>
        <DialogContent>
          <DialogHeader><DialogTitle>Add New Item</DialogTitle></DialogHeader>
          <ItemForm onSubmit={(data) => handleQuickAdd(Item, data, setIsItemFormOpen)} onCancel={() => setIsItemFormOpen(false)} />
        </DialogContent>
      </Dialog>
      <Dialog open={isWorkerFormOpen} onOpenChange={setIsWorkerFormOpen}>
        <DialogContent>
          <DialogHeader><DialogTitle>Add New Worker</DialogTitle></DialogHeader>
          <WorkerForm onSubmit={(data) => handleQuickAdd(Worker, data, setIsWorkerFormOpen)} onCancel={() => setIsWorkerFormOpen(false)} />
        </DialogContent>
      </Dialog>
    </div>
  );
}