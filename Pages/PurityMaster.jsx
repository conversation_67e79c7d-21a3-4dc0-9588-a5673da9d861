import React from 'react';
import MasterPage from '@/components/masters/MasterPage';
import { Purity } from '@/entities/Purity';
import { Badge } from '@/components/ui/badge';
import PurityForm from '../components/masters/PurityForm';

const columns = [
  { accessorKey: 'purity_name', header: 'Purity Name', sortable: true },
  { accessorKey: 'metal_type', header: 'Metal Type', sortable: true },
  { accessorKey: 'description', header: 'Description' },
  { 
    accessorKey: 'status', 
    header: 'Status', 
    sortable: true,
    cell: ({ row }) => (
      <Badge className={row.status === 'active' ? 'bg-emerald-100 text-emerald-800' : 'bg-slate-100 text-slate-800'}>
        {row.status}
      </Badge>
    )
  },
];

export default function PurityMaster() {
  return (
    <MasterPage
      entity={Purity}
      entityName="Purity"
      entityNamePlural="Purities"
      columns={columns}
      FormComponent={PurityForm}
    />
  );
}