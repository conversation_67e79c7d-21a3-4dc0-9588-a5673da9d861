# 🎯 Jewelry Pro - User Guide

## 🚀 Quick Start

### 1. **Access the Application**
- Open Chrome browser
- Navigate to: `http://localhost:3000`
- You'll see the beautiful login screen

### 2. **Login**
- **Username:** `<EMAIL>`
- **Password:** `Admin@1234`
- Click "Sign In"

### 3. **Dashboard Overview**
After login, you'll see the comprehensive dashboard with:
- **Statistics Cards**: Customer count, items, sales, workers
- **Inventory Stats**: Item/Metal inward/outward with weights and trends
- **Quick Actions**: Fast access to add customers, items, workers, transactions
- **Recent Transactions**: Latest activity overview
- **Inventory Summary**: Current stock status

## 📋 **Main Features**

### 🏢 **Masters Management**

#### **Customer Master**
- Navigate: Sidebar → Masters → Customer Master
- **Add New**: Click "Add Customer" button
- **Fields**: Name*, Balance, Mobile, Email, Address, Remarks, Status
- **Actions**: Edit, Delete, Export, Search, Sort

#### **Item Master**
- Navigate: Sidebar → Masters → Item Master
- **Add New**: Click "Add Item" button
- **Fields**: Item Number*, Description*, Remarks, Status
- **Actions**: Edit, Delete, Export, Search, Sort

#### **Workers Master**
- Navigate: Sidebar → Masters → Workers Master
- **Add New**: Click "Add Worker" button
- **Fields**: Worker Name*, Balance, Remarks, Status
- **Actions**: Edit, Delete, Export, Search, Sort

#### **Metal Master**
- Navigate: Sidebar → Masters → Metal Master
- **Add New**: Click "Add Metal" button
- **Fields**: Metal Description*, Remarks, Status
- **Actions**: Edit, Delete, Export, Search, Sort

#### **Purity Master**
- Navigate: Sidebar → Masters → Purity Master
- **Add New**: Click "Add Purity" button
- **Fields**: Purity Name*, Metal Type*, Description, Remarks, Status
- **Actions**: Edit, Delete, Export, Search, Sort

### 📦 **Transactions Management**

#### **Item Inward**
- Navigate: Sidebar → Transactions → Item Inward
- **Purpose**: Record jewelry items received from customers
- **Fields**: Date*, Customer*, Item*, Melting, Gross Weight*, ST, EN, THD, Piece Count*, Net Weight*, Comments

#### **Item Outward**
- Navigate: Sidebar → Transactions → Item Outward
- **Purpose**: Record jewelry items delivered to customers
- **Fields**: Date*, Customer*, Item*, Melting, Gross Weight*, ST, EN, THD, Piece Count*, Net Weight*, Comments

#### **Metal Inward**
- Navigate: Sidebar → Transactions → Metal Inward
- **Purpose**: Record raw metals received
- **Fields**: Date*, Customer*, Metal*, Weight*, Purity*, Rate*, Piece Count*, Net Weight*, Comments

#### **Metal Outward**
- Navigate: Sidebar → Transactions → Metal Outward
- **Purpose**: Record metals sent for processing
- **Fields**: Date*, Customer*, Metal*, Weight*, Purity*, Rate*, Piece Count*, Net Weight*, Comments

#### **Sales**
- Navigate: Sidebar → Transactions → Sales
- **Purpose**: Record jewelry sales transactions
- **Fields**: Date*, Customer*, Item*, Melting, Gross Weight*, ST, EN, THD, Piece Count*, Net Weight*, Comments

### 📊 **History & Reports**
- Navigate: Sidebar → History
- **Features**:
  - View all transaction history
  - Filter by date range
  - Search across all fields
  - Export reports to CSV
  - Separate tabs for each transaction type

## 🎯 **How to Use**

### **Adding Records**
1. Navigate to the desired module
2. Click "Add [Entity]" button
3. Fill in required fields (marked with *)
4. Click "Save" or "Create"

### **Editing Records**
1. Find the record in the table
2. Click the "Edit" button (pencil icon)
3. Modify the fields
4. Click "Update"

### **Deleting Records**
1. Find the record in the table
2. Click the "Delete" button (trash icon)
3. Confirm the deletion

### **Searching**
1. Use the search box at the top of each table
2. Type any text to search across all fields
3. Results update in real-time

### **Exporting Data**
1. Click the "Export" button on any page
2. Data will download as CSV file
3. Open in Excel or any spreadsheet application

### **Filtering (History Page)**
1. Go to History page
2. Set start and end dates
3. Use search box for specific records
4. Click "Clear Filters" to reset

## 💡 **Tips & Best Practices**

### **Data Entry**
- Always fill required fields (marked with *)
- Use consistent naming conventions
- Add meaningful remarks for future reference
- Keep customer information up to date

### **Inventory Management**
- Record all inward transactions immediately
- Match outward transactions with inward records
- Regular reconciliation of weights and counts
- Monitor trends on the dashboard

### **Reporting**
- Export data regularly for backup
- Use date filters for period-specific reports
- Search function works across all fields
- Sort columns by clicking headers

## 🔧 **Troubleshooting**

### **Login Issues**
- Ensure correct credentials: `<EMAIL>` / `Admin@1234`
- Check for typos in username/password
- Clear browser cache if needed

### **Data Not Saving**
- Check all required fields are filled
- Ensure valid email format for email fields
- Check mobile number format (10-13 digits)
- Verify numeric fields have valid numbers

### **Performance**
- Application stores data locally in browser
- Clear browser data to reset all records
- Export data before clearing for backup

## 📱 **Mobile Usage**
- Application is fully responsive
- Works on tablets and smartphones
- Touch-friendly interface
- Sidebar collapses on mobile devices

## 🎉 **Sample Data**
The application comes pre-loaded with sample data:
- 4 sample customers
- 5 jewelry items
- 3 workers
- 4 metal types
- 4 purity standards
- Sample transactions for demonstration

## 🆘 **Support**
- All data is stored locally in your browser
- No internet connection required after initial load
- Data persists between browser sessions
- Use export feature for data backup

---

**Enjoy using Jewelry Pro for your inventory management needs!** 💎
