# Jewelry Pro - Complete Inventory Management System

A comprehensive, real-world jewelry inventory management system built with React, Vite, and Tailwind CSS. This application provides complete CRUD operations for managing jewelry manufacturing business operations.

## 🚀 Features

### Authentication
- Secure login with predefined credentials
- Username: `<EMAIL>`
- Password: `Admin@1234`
- Session management with localStorage

### Dashboard
- Real-time business intelligence widgets
- Recent transactions overview
- Inventory summary with trends
- Quick action buttons for common operations
- Sales metrics and analytics

### Masters Management
1. **Customer Master**
   - Auto-generated Customer ID
   - Customer details (name, balance, mobile, email, address)
   - Input validation and error handling
   - CRUD operations with search and export

2. **Item Master**
   - Auto-generated Item ID
   - Item number and description management
   - Status tracking (Active/Inactive)

3. **Workers Master**
   - Worker registration and balance tracking
   - Performance and remarks management

4. **Metal Master**
   - Metal type and description management
   - Quality specifications

5. **Purity Master**
   - Purity standards for different metals
   - Metal type associations

### Transactions Management
1. **Item Inward**
   - Record jewelry items received
   - Weight tracking (gross/net)
   - Customer and item associations
   - Melting details and piece count

2. **Item Outward**
   - Track jewelry items delivered
   - Complete transaction records
   - Customer delivery management

3. **Metal Inward**
   - Raw metal receipt tracking
   - Purity and rate management
   - Supplier transaction records

4. **Metal Outward**
   - Metal distribution tracking
   - Manufacturing allocation

5. **Sales Module**
   - Complete sales transaction management
   - Customer and item tracking
   - Revenue and inventory impact

### History & Reports
- Comprehensive reporting system
- Date range filtering
- Advanced search capabilities
- Export functionality (CSV format)
- Transaction history for all modules

## 🛠️ Technical Features

### Frontend Architecture
- **React 18** with functional components and hooks
- **Vite** for fast development and building
- **Tailwind CSS** for responsive design
- **Lucide React** for modern icons
- **React Router DOM** for navigation

### Data Management
- **JSON-based storage** in localStorage
- **Entity-based architecture** with base classes
- **Real-time data synchronization**
- **Automatic ID generation**
- **Data validation and error handling**

### UI/UX Features
- **Responsive design** for all screen sizes
- **Advanced form validation**
- **Loading states and error handling**
- **Modern glassmorphism design**
- **Smooth animations and transitions**
- **Accessible components**

### Business Logic
- **Automatic audit trails** (created_by, updated_by, timestamps)
- **Status management** (Active/Inactive)
- **Relationship management** between entities
- **Data integrity validation**
- **Export functionality** for all data

## 🚀 Getting Started

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd jewelry-inventory-system
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npm run dev
   ```

4. **Open in browser**
   ```
   http://localhost:3000
   ```

### Login Credentials
- **Username:** <EMAIL>
- **Password:** Admin@1234

## 📱 Application Structure

```
src/
├── components/
│   ├── ui/                 # Reusable UI components
│   ├── dashboard/          # Dashboard-specific components
│   ├── masters/            # Master data forms and pages
│   ├── transactions/       # Transaction forms
│   ├── history/           # Reporting components
│   └── common/            # Shared components
├── entities/              # Data entities and business logic
├── pages/                 # Main application pages
├── utils/                 # Utility functions and helpers
└── Layout.jsx            # Main application layout
```

## 🎯 Key Functionalities

### Data Management
- **Create:** Add new records with validation
- **Read:** View and search existing records
- **Update:** Edit records with audit trails
- **Delete:** Remove records with confirmation
- **Export:** Download data in CSV format

### Search & Filter
- **Global search** across all fields
- **Date range filtering** for transactions
- **Status-based filtering**
- **Real-time search results**

### Validation
- **Email format validation**
- **Mobile number validation** (10-13 digits)
- **Required field validation**
- **Numeric field validation**
- **Date validation**

### User Experience
- **Responsive design** for mobile and desktop
- **Loading indicators** for async operations
- **Error messages** with clear guidance
- **Success notifications**
- **Intuitive navigation**

## 🔧 Customization

### Adding New Entities
1. Create entity class in `src/entities/`
2. Add to `src/entities/all.js`
3. Create form component in appropriate folder
4. Add page component in `src/pages/`
5. Update routing in `App.jsx`

### Styling
- Modify `src/index.css` for global styles
- Update Tailwind configuration in `tailwind.config.js`
- Customize component styles in individual files

## 📊 Sample Data

The application includes sample data for demonstration:
- 4 sample customers with complete profiles
- 5 jewelry items with descriptions
- 3 workers with specializations
- 4 metal types with purity standards
- Sample transactions across all modules

## 🚀 Production Deployment

### Build for Production
```bash
npm run build
```

### Preview Production Build
```bash
npm run preview
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Check the documentation
- Review the code comments
- Create an issue in the repository

---

**Jewelry Pro** - Professional inventory management for jewelry manufacturing businesses.
